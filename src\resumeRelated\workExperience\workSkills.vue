<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="拥有技能">
        <template #right>
          <view @click="submit">确认</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="corporateName">
      <wd-input focus no-border v-model="skills" placeholder="请输入工作技能(逗号隔开)" />
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { useResumeStore } from '@/store'
// 技能
const resumeStore = useResumeStore()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 初始化
const initNane = ref('')
const skills = ref('')
onLoad(async (options) => {
  await nextTick()
  skills.value = options.skills
  initNane.value = options.skills
})
// 返回
const back = () => {
  if (skills.value !== initNane.value) {
    uni.showModal({
      title: '提示',
      content: '您有内容未提交保存,确认返回吗?',
      // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
      success: function (res) {
        if (res.confirm) {
          resumeStore.setSkills(initNane.value)
          uni.navigateBack()
          // console.log('用户点击了确定');
        }
      },
    })
  } else {
    uni.navigateBack()
  }
}
const submit = () => {
  resumeStore.setSkills(skills.value)
  uni.navigateBack()
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
::v-deep .wd-input__placeholder {
  font-size: 32rpx !important;
}
::v-deep .wd-input__inner {
  font-size: 32rpx !important;
  font-weight: 500;
}
.corporateName {
  padding-bottom: 40rpx;
  margin: 40rpx 40rpx;
  border-bottom: 1rpx solid #c0bfbf;
}
</style>
