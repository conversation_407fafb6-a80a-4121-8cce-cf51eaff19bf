<template>
  <view class="flex flex-col gap-34rpx">
    <view
      class="flex items-center gap-20rpx"
      v-for="(item, key) in newsList"
      :key="`news-list-${key}`"
    >
      <wd-img width="68rpx" height="68rpx" round custom-class="bg-red" />
      <view class="flex flex-col gap-4rpx flex-1">
        <view class="flex items-center gap-4rpx">
          <text class="c-#555555 text-28rpx font-500 flex-1 line-clamp-1">
            {{ item.name }}·电商运营
          </text>
          <text class="c-#888888 text-22rpx">4月1日 11:39</text>
        </view>
        <text class="line-clamp-1 c-#888888 text-22rpx ml--10rpx">
          「新招呼」文本文案文本文案文本文案文文本文案文本文案
        </text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
interface propsInt {
  newsList: AnyObject[]
}

withDefaults(defineProps<propsInt>(), {
  newsList: () => [],
})
</script>

<style lang="scss" scoped>
//
</style>
