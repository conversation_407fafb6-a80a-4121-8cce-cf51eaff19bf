<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="我的在线简历">
        <template v-slot:right>
          <view class="CustomNavBar-right" @click="goPreview">预览</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="onlineRes">
      <view class="onlineRes-list flex-between">
        <view class="onlineRes-left">
          <view class="onlineRes-info flex-c">
            <view class="flex-c">
              <view class="name">{{ onlineObj.userName }}</view>
              <!-- <image
                v-if="onlineObj.sex === 1"
                class="start_ICON_item-1"
                src="@/resumeRelated/img/<EMAIL>"
              ></image>
              <image
                v-if="onlineObj.sex === 2"
                class="start_ICON_item-1"
                src="@/resumeRelated/img/sex.png"
              ></image> -->
            </view>

            <view class="onlineRes-rz flex-c" @click="goCheackInfo">
              <view class="name-rz nomal">未认证</view>
              <wd-icon name="warning" size="15px" color="#FF0000"></wd-icon>
            </view>
            <!-- <view class="onlineRes-rz flex-c" v-else>
              <view class="name-rz active">已认证</view>
              <wd-icon name="warning" size="15px" color="#333"></wd-icon>
            </view> -->
          </view>
          <view class="onlineRes-my flex items-center">
            <view v-if="onlineObj?.workYear" class="onlineRes-my-item flex items-center">
              <wd-img :width="16" :height="16" :src="icons" />
              <text class="c-#888 text-26rpx">
                {{
                  onlineObj?.workYear > 0 && onlineObj?.workYear <= 10
                    ? onlineObj.workYear + '年'
                    : onlineObj.workYear > 10
                      ? onlineObj.workYear + '年以上'
                      : ''
                }}
              </text>
            </view>
            <view v-if="onlineObj?.age" class="onlineRes-my-item flex items-center">
              <wd-img :width="16" :height="16" :src="birthdayIcon" />
              <text class="c-#888 text-26rpx">{{ onlineObj.age }}岁</text>
              <!-- <text class="c-#888 text-26rpx" v-if="onlineObj.xueLi">·</text> -->
            </view>
            <view v-if="onlineObj?.xueLi" class="onlineRes-my-item flex items-center">
              <wd-img :width="16" :height="16" :src="educationIcon" />
              <text class="c-#888 text-26rpx">
                {{
                  onlineObj.xueLi === 1
                    ? '高中及以下'
                    : onlineObj.xueLi === 2
                      ? '专科'
                      : onlineObj.xueLi === 3
                        ? '本科'
                        : onlineObj.xueLi === 4
                          ? '硕士'
                          : onlineObj.xueLi === 4
                            ? '博士及以上'
                            : ''
                }}
              </text>
            </view>
          </view>
          <view class="onlineRes-connect flex-c">
            <view class="flex-c m-right">
              <image
                class="onlineRes-connect-img"
                src="@/resumeRelated/img/Group_1171274957.png"
              ></image>
              <view class="onlineRes-connect-name">{{ onlineObj.telephone }}</view>
            </view>
            <view class="flex-c">
              <image
                class="onlineRes-connect-img-1"
                src="@/resumeRelated/img/Group_1171274958.png"
              ></image>
              <view class="onlineRes-connect-name" v-if="onlineObj.wxCode">
                {{ onlineObj.wxCode }}
              </view>
              <view class="onlineRes-connect-name c-#4d8fff" v-else @click="goWxUpdata">
                去填写
              </view>
            </view>
          </view>
        </view>
        <view class="onlineRes-right text-right">
          <!-- <image src="/static/img/1.jpg"></image> -->
          <image
            v-if="onlineObj.sex === 1"
            class="border-boy"
            :src="onlineObj.headImgUrl ? onlineObj.headImgUrl : '/static/header/jobhunting1.png'"
            mode="aspectFill"
          ></image>
          <image
            v-else
            class="border-griy"
            :src="onlineObj.headImgUrl ? onlineObj.headImgUrl : '/static/header/jobhunting2.png'"
            mode="aspectFill"
          ></image>
        </view>
      </view>
      <view class="onlineRes-job flex-between">
        <view class="onlineRes-job-left flex-1">
          <view class="onlineRes-title">求职状态</view>
          <wd-picker
            custom-view-class="custom-view-class"
            :columns="qzList"
            label=""
            v-model="onlineObj.seekStatus"
            @confirm="submitJob"
          />
        </view>
        <view class="">
          <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon>
        </view>
      </view>
      <view class="work-qw">
        <view class="jobExpectations-title flex-between">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">个人亮点</view>
          </view>
          <view class="jobExpectations-right">
            <wd-icon
              name="add-circle1"
              @click="goMyAdvantage"
              class="p-l-20rpx"
              color="#000000"
              size="18px"
            ></wd-icon>
          </view>
        </view>
        <view v-if="onlineObj.myLights" class="text-container">
          <view
            class="my-lights-text"
            :class="{
              expanded: myLightsExpanded,
              collapsed: !myLightsExpanded && showMyLightsMore,
            }"
            @click="goEditMyAdvantage(onlineObj.myLights)"
          >
            <text class="text-pre-wrap text-24rpx c-#666">
              {{ onlineObj.myLights }}
              <text
                v-if="showMyLightsMore"
                class="view-detail text-24rpx"
                @click.stop="showMyLightsDetail"
                style="color: #457ae6"
              >
                ...查看详情
              </text>
            </text>
          </view>
          <text
            v-if="showMyLightsCollapse && !showMyLightsMore"
            class="view-detail text-24rpx"
            @click="showMyLightsShrink"
            style="color: #457ae6"
          >
            收起
          </text>
        </view>
      </view>
      <view class="jobExpectations">
        <view class="jobExpectations-title flex-between">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx p-b-10rpx">求职期望</view>
            <wd-tooltip placement="right-start" content="完善求职期望，为你智能匹配更多优质职位!">
              <wd-icon name="help-circle" color="#000000" size="18px"></wd-icon>
            </wd-tooltip>
          </view>
          <view class="jobExpectations-right">
            <wd-icon
              name="add-circle1"
              @click="goSeekEmployment"
              class="p-l-10rpx"
              color="#000000"
              size="18px"
            ></wd-icon>
          </view>
        </view>
        <view
          v-for="(item, index) in onlineObj.jobIntentionList"
          :key="index"
          @click="goEditSeekEmployment(item)"
        >
          <view class="jobExpectations-qw text-28rpx">
            {{ item.jobType === 1 ? '全职期望' : '兼职期望' }}
          </view>
          <view class="jobExpectations-exepress flex-between">
            <view class="jobExpectations-exepress-left">
              <view class="jobExpectations-exepress-list">
                <view class="flex-c">
                  <view class="text-name text-28rpx">{{ item.expectedPositions }}</view>
                  <view class="text-name text-salary text-28rpx">
                    {{ item.salaryExpectationStart }}
                    <text v-if="item.salaryExpectationEnd">-</text>
                    {{ item.salaryExpectationEnd }}
                  </view>
                </view>
                <view class="flex-c">
                  <view class="text-position">{{ item.provinceName }}</view>
                  <view class="text-position text-denery">{{ item.expectedIndustry }}</view>
                </view>
              </view>
            </view>
            <view class="jobExpectations-exepress-right">
              <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon>
            </view>
          </view>
        </view>
      </view>
      <view class="education">
        <view class="jobExpectations-title flex-between m-b-30rpx">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">教育经历</view>
          </view>
          <view class="jobExpectations-right">
            <wd-icon
              name="add-circle1"
              @click="goEducation"
              class="p-l-20rpx"
              color="#000000"
              size="18px"
            ></wd-icon>
          </view>
        </view>
        <view
          class="education-list flex-between"
          v-for="(item, index) in onlineObj.educationsList"
          :key="index"
          @click="goEducationEdit(item)"
        >
          <view class="education-left flex-c">
            <view class="education-left-img"></view>
            <view class="education-left-xl">
              <view class="text-28rpx c-#333">{{ item.school }}</view>
              <view class="education-left-xl-subname text-28rpx c-#888">{{ item.major }}</view>
            </view>
          </view>
          <view class="education-right flex-between">
            <view></view>
            <view class="flex-c">
              <view class="time">
                {{ item.startTime.substring(0, 4) }}-{{ item.endTime.substring(0, 4) }}
              </view>
              <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon>
            </view>
          </view>
        </view>
      </view>
      <view class="work-qw">
        <view class="jobExpectations-title flex-between">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">工作经历</view>
          </view>
          <view class="jobExpectations-right">
            <wd-icon
              name="add-circle1"
              @click="goWorkExperience"
              class="p-l-20rpx"
              color="#000000"
              size="18px"
            ></wd-icon>
          </view>
        </view>
        <view>
          <view
            class="work-qw-content"
            v-for="(item, index) in onlineObj.workExperiencesList"
            :key="index"
            @click="editWork(item)"
          >
            <view class="work-qw-title flex-between">
              <view class="flex-c">
                <view class="text-28rpx c-#000">{{ item.company }}</view>
                <!-- <view class="m-l-20rpx">{{}}-13000</view> -->
              </view>
              <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon>
            </view>
            <view class="flex-between work-qw-line">
              <view class="c-#888 text-24rpx">{{ item.industry }}</view>
              <view class="c-#888 text-22rpx">
                {{ item.startTime.slice(0, 7) }}至{{ item.endTime.slice(0, 7) }}
              </view>
            </view>
            <view class="p-b-10rpx p-t-10rpx">
              <view class="text-container">
                <view
                  class="work-desc-text"
                  :class="{
                    expanded: workExpandedStates[index],
                    collapsed: !workExpandedStates[index] && shouldShowWorkMore(item, index),
                  }"
                >
                  <text class="text-pre-wrap c-#888 text-24rpx">
                    内容：{{ item.workDescription }}
                    <text
                      v-if="shouldShowWorkMore(item, index)"
                      class="view-detail text-24rpx"
                      @click.stop="showWorkDetail(index)"
                      style="color: #457ae6"
                    >
                      ...查看详情
                    </text>
                  </text>
                </view>
                <text
                  v-if="shouldShowWorkCollapse(item, index)"
                  class="view-detail text-24rpx"
                  @click.stop="showWorkShrink(index)"
                  style="color: #457ae6"
                >
                  收起
                </text>
              </view>
            </view>
            <!-- <view class="wx-tag">文案編輯</view> -->
          </view>
        </view>
      </view>
      <view class="work-qw">
        <view class="jobExpectations-title flex-between">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">项目经历</view>
          </view>
          <view class="jobExpectations-right">
            <wd-icon
              name="add-circle1"
              @click="goProjectExperience"
              class="p-l-20rpx"
              color="#000000"
              size="18px"
            ></wd-icon>
          </view>
        </view>

        <view
          class="work-qw-content"
          v-for="(item, index) in onlineObj.projectList"
          :key="index"
          @click="editProject(item)"
        >
          <view class="work-qw-title flex-between">
            <view class="flex-c">
              <view class="text-28rpx c-#000">{{ item.projectName }}</view>
            </view>
            <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon>
          </view>
          <view class="flex-between work-qw-line">
            <view class="c-#888 text-24rpx">{{ item.takeOffice }}</view>
            <view class="c-#888 text-22rpx">
              {{ item.startDate.slice(0, 7) }}-{{ item.endDate.slice(0, 7) }}
            </view>
          </view>
          <view class="p-b-10rpx p-t-10rpx">
            <view class="text-container">
              <view
                class="project-desc-text"
                :class="{
                  expanded: projectExpandedStates[index],
                  collapsed: !projectExpandedStates[index] && shouldShowProjectMore(item, index),
                }"
              >
                <text class="text-pre-wrap c-#888 text-24rpx">
                  内容：{{ item.projectDescs }}
                  <text
                    v-if="shouldShowProjectMore(item, index)"
                    class="view-detail text-24rpx"
                    @click.stop="showProjectDetail(index)"
                    style="color: #457ae6"
                  >
                    ...查看详情
                  </text>
                </text>
              </view>
              <text
                v-if="shouldShowProjectCollapse(item, index)"
                class="view-detail text-24rpx"
                @click.stop="showProjectShrink(index)"
                style="color: #457ae6"
              >
                收起
              </text>
            </view>
          </view>
        </view>
      </view>
      <view class="qualification">
        <view class="jobExpectations-title flex-between">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">资格证书</view>
            <wd-tooltip placement="right-start" content="添加相关资格证书，提升竞争优势！">
              <wd-icon name="help-circle" color="#000000" size="18px"></wd-icon>
            </wd-tooltip>
          </view>
          <view class="jobExpectations-right">
            <wd-icon
              name="add-circle1"
              @click="goCertificate"
              class="p-l-10rpx"
              color="#000000"
              size="18px"
            ></wd-icon>
          </view>
        </view>
        <view class="qualification-list flex-c">
          <view
            @click="goCertificate"
            class="qualification-tag"
            v-for="(item, index) in onlineObj.resumeCertificateVOList"
            :key="index"
          >
            <!-- <image :src="item.pic" mode=""></image> -->
            <view class="qualification-tag-name">
              {{ item.certificate }}
            </view>
          </view>
        </view>
      </view>
      <view class="qualification">
        <view class="jobExpectations-title flex-between">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">掌握技能</view>
            <wd-tooltip placement="right-start" content="填写掌握技能，突出核心竞争力！">
              <wd-icon name="help-circle" color="#000000" size="18px"></wd-icon>
            </wd-tooltip>
          </view>
          <view class="jobExpectations-right" @click="goSkill">
            <wd-icon name="add-circle1" color="#000000" size="18px"></wd-icon>
          </view>
        </view>

        <view class="qualification-list flex-c">
          <view
            class="qualification-tag"
            @click="editSkill(onlineObj.skills)"
            v-for="(item, index) in onlineObj.skills"
            :key="index"
          >
            <view class="qualification-tag-name">
              {{ item }}
            </view>
          </view>
        </view>
      </view>
      <view class="Portfolio">
        <view class="jobExpectations-title flex-between">
          <view class="jobExpectations-left flex-c">
            <view class="onlineRes-title m-r-10rpx">作品集上传</view>
          </view>
          <view class="jobExpectations-right" @click="goPortfolio(onlineObj.resumeFileVOList)">
            <wd-icon name="add-circle1" color="#000000" size="18px"></wd-icon>
          </view>
        </view>
        <view class="Portfolio-subtitle onlineRes-subtitle">不得大于20MB，图片格式</view>
        <view class="Portfolio-input">
          <wd-input readonly v-model="url" no-border placeholder="作品集链接" />
        </view>
        <view class="Portfolio-upload">
          <image src="/static/img/zuimeizuopinji_1.png" class="Portfolio-upload-img"></image>
          <view class="Portfolio-text onlineRes-subtitle p-t-10rpx">
            上传作品集上传「不得大于20MB」
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>
<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryFullData, updateStatus } from '@/interPost/resume'
import { useResumeStore } from '@/store'
import { numberTokw } from '@/utils/common'
import icons from '@/resumeRelated/img/icons.png'
import birthdayIcon from '@/resumeRelated/img/birthday_icon.png'
import educationIcon from '@/resumeRelated/img/education_icon.png'

const resumeStore = useResumeStore()
const onlineObj = ref<AnyObject>({})
const textState1 = ref(false)
const url = ref('')

// 个人优势文本处理相关变量
const myLightsText = ref('') // 显示的个人优势文本
const myLightsOriginal = ref('') // 原始个人优势文本
const showMyLightsMore = ref(false) // 是否显示"查看详情"按钮
const showMyLightsCollapse = ref(false) // 是否显示"收起"按钮
const myLightsExpanded = ref(false) // 是否已展开

// 工作经历文本处理相关变量
const workExpandedStates = ref<Record<number, boolean>>({}) // 工作经历展开状态

// 项目经历文本处理相关变量
const projectExpandedStates = ref<Record<number, boolean>>({}) // 项目经历展开状态
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

onShow(() => {
  // 获取简历
  getList()
})
const qzList = reactive([
  {
    label: '离职-随时到岗',
    value: 0,
  },
  {
    label: '在职-考虑机会',
    value: 1,
  },
  {
    label: '观望机会',
    value: 2,
  },
])
const goCheackInfo = () => {
  const idCard = ''
  const trueName = onlineObj.value.userName
  uni.navigateTo({
    url: `/setting/identityAuth/index?idCard=${idCard}&trueName=${trueName}`,
  })
}
// 获取简历信息
const getList = async () => {
  await uni.$onLaunched
  const res: any = await queryFullData()
  if (res.code === 0) {
    if (
      Array.isArray(res.data.skills) &&
      res.data.skills.length === 1 &&
      res.data.skills[0] === ''
    ) {
      res.data.skills = []
    }
    url.value = res.data.resumeFileVOList.length > 0 ? res.data.resumeFileVOList[0].url : ''
    res.data.jobIntentionList.forEach((ele: any) => {
      ele.salaryExpectationStart =
        ele.salaryExpectationStart === 0 ? '面议' : numberTokw(ele.salaryExpectationStart + '')
      ele.salaryExpectationEnd =
        ele.salaryExpectationEnd === 0 ? '' : numberTokw(ele.salaryExpectationEnd + '')
      if (ele.distanceMeters)
        ele.distanceMeters = Math.floor(parseInt(ele.distanceMeters) / 1000) + 'km'
    })
    onlineObj.value = res.data

    // 处理个人优势文本
    if (res.data.myLights) {
      myLightsOriginal.value = res.data.myLights
      checkMyLightsTextLength()
    }

    // 初始化工作经历展开状态
    if (res.data.workExperiencesList) {
      workExpandedStates.value = {}
      res.data.workExperiencesList.forEach((_, index) => {
        workExpandedStates.value[index] = false
      })
    }

    // 初始化项目经历展开状态
    if (res.data.projectList) {
      projectExpandedStates.value = {}
      res.data.projectList.forEach((_, index) => {
        projectExpandedStates.value[index] = false
      })
    }
  }
}
const submitJob = async (e: any) => {
  const res: any = await updateStatus({ id: onlineObj.value.id, seekStatus: e.selectedItems.value })

  if (res.code === 1) {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 微信
const goWxUpdata = () => {
  uni.navigateTo({
    url: '/setting/wxUpdata/index',
  })
}
// 上传作品集
const goPortfolio = (item: any) => {
  const list = onlineObj.value.resumeFileVOList?.length > 0 ? 'edit' : 'add'
  const attachmentId =
    onlineObj.value.resumeFileVOList?.length > 0 &&
    onlineObj.value.resumeFileVOList[0]?.attachmentId
      ? onlineObj.value.resumeFileVOList[0]?.attachmentId
      : ''
  const url =
    onlineObj.value.resumeFileVOList?.length > 0 && onlineObj.value.resumeFileVOList[0]?.url
      ? onlineObj.value.resumeFileVOList[0]?.url
      : ''
  const sid =
    onlineObj.value.resumeFileVOList?.length > 0 && onlineObj.value.resumeFileVOList[0]?.id
      ? onlineObj.value.resumeFileVOList[0]?.id
      : ''

  if (list === 'add') {
    uni.navigateTo({
      url: `/resumeRelated/onlineResume/portfolio/index?id=${onlineObj.value.id}&isAdd=${list}&sid=${sid}&attachmentId=${attachmentId}&url=${url}`,
    })
  } else {
    uni.navigateTo({
      url: `/resumeRelated/onlineResume/portfolio/index?id=${onlineObj.value.id}&isAdd=${list}&sid=${sid}&attachmentId=${attachmentId}&url=${url}`,
    })
  }
}
// 新增个人优势
const goMyAdvantage = () => {
  uni.navigateTo({
    url: `/resumeRelated/onlineResume/myAdvantage/index?id=${onlineObj.value.id}&isAdd=add&myLights=${onlineObj.value.myLights}`,
  })
}
// 编辑个人优势
const goEditMyAdvantage = (obj: string) => {
  uni.navigateTo({
    url: `/resumeRelated/onlineResume/myAdvantage/index?id=${onlineObj.value.id}&isAdd=add&myLights=${obj}`,
  })
}

// 检查个人优势文本是否需要展开/收起功能
const checkMyLightsTextLength = () => {
  if (!myLightsOriginal.value) {
    showMyLightsMore.value = false
    showMyLightsCollapse.value = false
    return
  }

  // 简单的行数估算：假设每行大约30个字符（可根据实际情况调整）
  const estimatedLines = Math.ceil(myLightsOriginal.value.length / 30)

  if (estimatedLines > 2) {
    if (!myLightsExpanded.value) {
      showMyLightsMore.value = true
      showMyLightsCollapse.value = false
    } else {
      showMyLightsMore.value = false
      showMyLightsCollapse.value = true
    }
  } else {
    showMyLightsMore.value = false
    showMyLightsCollapse.value = false
  }
}

// 显示个人优势详情
const showMyLightsDetail = () => {
  if (showMyLightsMore.value) {
    myLightsExpanded.value = true
    checkMyLightsTextLength()
  }
}

// 收起个人优势
const showMyLightsShrink = () => {
  if (showMyLightsCollapse.value) {
    myLightsExpanded.value = false
    checkMyLightsTextLength()
  }
}

// 工作经历相关方法

const shouldShowWorkMore = (item: any, index: number) => {
  if (!item.workDescription) return false
  // 简单的行数估算：假设每行大约30个字符
  const estimatedLines = Math.ceil(item.workDescription.length / 30)
  return estimatedLines > 2 && !workExpandedStates.value[index]
}

const shouldShowWorkCollapse = (item: any, index: number) => {
  if (!item.workDescription) return false
  // 简单的行数估算：假设每行大约30个字符
  const estimatedLines = Math.ceil(item.workDescription.length / 30)
  return estimatedLines > 2 && workExpandedStates.value[index]
}

const showWorkDetail = (index: number) => {
  workExpandedStates.value[index] = true
}

const showWorkShrink = (index: number) => {
  workExpandedStates.value[index] = false
}

// 项目经历相关方法
const shouldShowProjectMore = (item: any, index: number) => {
  if (!item.projectDescs) return false
  // 简单的行数估算：假设每行大约30个字符
  const estimatedLines = Math.ceil(item.projectDescs.length / 30)
  return estimatedLines > 2 && !projectExpandedStates.value[index]
}

const shouldShowProjectCollapse = (item: any, index: number) => {
  if (!item.projectDescs) return false
  // 简单的行数估算：假设每行大约30个字符
  const estimatedLines = Math.ceil(item.projectDescs.length / 30)
  return estimatedLines > 2 && projectExpandedStates.value[index]
}

const showProjectDetail = (index: number) => {
  projectExpandedStates.value[index] = true
}

const showProjectShrink = (index: number) => {
  projectExpandedStates.value[index] = false
}
// 新增工作经历
const goWorkExperience = () => {
  resumeStore.setisAdd('add')
  uni.navigateTo({
    url: `/resumeRelated/workExperience/index?id=${onlineObj.value.id}`,
  })
}
// 编辑工作经历
const editWork = (item) => {
  resumeStore.setisAdd('edit')
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/resumeRelated/workExperience/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}`,
  })
}
// 项目经历
const goProjectExperience = () => {
  uni.navigateTo({
    url: `/resumeRelated/projectExperience/index?id=${onlineObj.value.id}&isAdd=add`,
  })
}
// 编辑项目经历
const editProject = async (item) => {
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/resumeRelated/projectExperience/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}
// 技能证书
const goSkill = () => {
  const str = JSON.stringify(onlineObj.value.skills)
  uni.navigateTo({
    url: `/resumeRelated/mySkill/index?id=${onlineObj.value.id}&isAdd=add&item=${encodeURIComponent(str)}`,
  })
}
// 编辑
const editSkill = (item: any) => {
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/resumeRelated/mySkill/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}
const goCertificate = () => {
  uni.navigateTo({
    url: `/resumeRelated/onlineResume/qualiCertificate/index?id=${onlineObj.value.id}`,
  })
}
// 求职期望
const goSeekEmployment = () => {
  uni.navigateTo({
    url: `/resumeRelated/seekEmployment/index?id=${onlineObj.value.id}&isAdd=add`,
  })
}
// 编辑求职期望
const goEditSeekEmployment = (item: any) => {
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/resumeRelated/seekEmployment/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}
// 新增简历
const goEducation = () => {
  uni.navigateTo({
    url: `/resumeRelated/education/index?id=${onlineObj.value.id}&isAdd=add`,
  })
}
// 编辑简历
const goEducationEdit = (item) => {
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/resumeRelated/education/index?id=${onlineObj.value.id}&item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}

// 预览
const goPreview = () => {
  uni.navigateTo({
    url: '/resumeRelated/preview/index',
  })
}
</script>

<style scoped lang="scss">
::v-deep .wd-picker__value {
  font-size: 28rpx;
  color: #333;
}
::v-deep .wd-input__value {
  padding: 20rpx 20rpx !important;
  background-color: #e3e3e3;
  border-radius: 20rpx;
}
::v-deep .wd-input {
  background-color: transparent !important;
}
::v-deep .wd-picker__cell {
  width: 100% !important;
  padding-left: 0rpx !important;
  background: transparent !important;
}
::v-deep .wd-picker__arrow {
  display: none;
}
.border-boy {
  border: 3rpx solid #3e9cff;
}
.border-griy {
  border: 3rpx solid rgba(255, 190, 190, 1);
}
.custom-class {
  padding: 20rpx 0rpx 10rpx !important;
  background: transparent !important;
}
.CustomNavBar-right {
  font-size: 28rpx;
  color: #888888;
}
.start_ICON_item {
  width: 40rpx;
  height: 40rpx;
}
.start_ICON_item-1 {
  width: 35rpx;
  height: 35rpx;
}
.onlineRes-rz {
  margin-left: 30rpx;
}

.name-rz {
  margin-right: 4rpx;
  font-size: 26rpx;
}
.active {
  color: #000 !important;
}
.nomal {
  color: #ff0000 !important;
}
:deep .u-input {
  height: 80rpx;
  background-color: #e3e3e3 !important;
  border-radius: 20rpx !important;
}

.onlineRes-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.onlineRes-subtitle {
  font-size: 24rpx;
  color: #666666;
}

.work-qw-line {
  line-height: 50rpx;
}

.onlineRes {
  padding: 40rpx;

  .onlineRes-list {
    padding-bottom: 30rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .onlineRes-left {
      width: 70%;

      .onlineRes-info {
        .name {
          font-size: 32rpx;
          font-weight: bold;
          color: #000000;
        }

        .onlineRes-rz {
          margin-left: 50rpx;

          .name-rz {
            margin-right: 4rpx;
            font-size: 26rpx;
            color: #ff0000;
          }
        }
      }

      .onlineRes-my {
        font-size: 26rpx;
        line-height: 50rpx;
        color: #888888;

        .onlineRes-my-item {
          margin-right: 20rpx;
          display: flex;
          align-items: center;
          gap: 8rpx;

          // 确保图标和文本垂直居中对齐
          :deep(wd-img) {
            display: flex;
            align-items: center;
            flex-shrink: 0; // 防止图标被压缩
          }

          text {
            line-height: 1.2; // 设置合适的行高
            display: flex;
            align-items: center;
          }
        }
      }

      .onlineRes-connect {
        .m-right {
          margin-right: 50rpx;
        }

        .onlineRes-connect-img {
          width: 24rpx;
          height: 28rpx !important;
        }

        .onlineRes-connect-img-1 {
          width: 30rpx;
          height: 26rpx;
        }

        .onlineRes-connect-name {
          padding-left: 5rpx;
          font-size: 26rpx;
          color: #888888;
        }
      }
    }

    .onlineRes-right {
      width: 30%;

      image {
        width: 120rpx;
        height: 120rpx;
        margin: 0 0 0 auto;
        border-radius: 50%;
      }
    }
  }

  .onlineRes-job {
    padding: 30rpx 0 15rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .onlineRes-job-left {
      .onlineRes-time {
        padding-top: 10rpx;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }

  .jobExpectations {
    padding: 30rpx 0rpx 20rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .jobExpectations-qw {
      line-height: 60rpx;
    }

    .jobExpectations-exepress {
      padding-bottom: 20rpx;

      .jobExpectations-exepress-left {
        .text-name {
          line-height: 60rpx;
        }

        .text-salary {
          padding-left: 20rpx;
        }

        .text-position {
          font-size: 24rpx;
          color: #888888;
        }

        .text-denery {
          padding-left: 40rpx;
        }
      }
    }
  }

  .work-qw {
    padding-top: 50rpx;

    .jobExpectations-exepress {
      .jobExpectations-exepress-left {
        .text-name {
          line-height: 60rpx;
        }

        .text-salary {
          padding-left: 20rpx;
        }

        .text-position {
          font-size: 24rpx;
          color: #888888;
        }

        .text-denery {
          padding-left: 40rpx;
        }
      }
    }

    .work-qw-title {
      line-height: 60rpx;
    }

    .work-qw-content {
      padding: 10rpx 0rpx 30rpx;
      border-bottom: 1rpx solid #d7d6d6;
    }

    .wx-tag {
      width: 160rpx;
      padding: 2rpx 5rpx;
      font-size: 24rpx;
      color: #888888;
      text-align: center;
      background-color: #e5e5e5;
      border-radius: 10rpx;
    }
  }

  .education {
    padding-top: 30rpx;
    padding-bottom: 10rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .education-list {
      padding: 0rpx 0rpx 30rpx;

      .education-left {
        width: 70%;

        .education-left-img {
          width: 80rpx;
          height: 80rpx;
          margin-right: 20rpx;
          background-image: url('/static/img/Group_1171274967.png');
          background-position: 100% 100%;
          background-size: 100% 100%;
        }

        .education-left-xl {
          .education-left-xl-subname {
            color: #888888;
          }
        }
      }

      .education-right {
        width: 30%;

        .time {
          font-size: 22rpx;
          color: #888888;
        }
      }
    }
  }

  .qualification {
    padding: 50rpx 0 17rpx 0;
    border-bottom: 1rpx solid #d7d6d6;

    .qualification-list {
      flex-wrap: wrap !important;
      width: 100%;
      padding: 10rpx 0rpx 30rpx;

      .qualification-tag {
        // width: 33%;
        padding-top: 20rpx;
        text-align: center;

        .qualification-tag-name {
          padding: 5rpx 20rpx;
          margin-right: 10rpx;
          font-size: 22rpx;
          color: #888888;
          background-color: #d9d9d9;
          border-radius: 10rpx;
        }
      }
    }
  }

  .Portfolio {
    padding: 30rpx 0rpx 160rpx;

    .Portfolio-subtitle {
      padding-bottom: 40rpx;
    }

    .Portfolio-input {
      padding-bottom: 40rpx;
    }

    .Portfolio-upload {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .Portfolio-upload-img {
        width: 120rpx;
        height: 120rpx;
      }
    }
  }

  .text-container {
    .view-detail {
      color: #457ae6;
      cursor: pointer;
      margin-left: 10rpx;

      &:hover {
        text-decoration: underline;
      }
    }

    .edit-link {
      color: #457ae6;
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    // 个人亮点文本样式
    .my-lights-text {
      &.collapsed {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        line-height: 1.5;

        .view-detail {
          float: right;
          background: #fff;
          margin-left: 10rpx;
        }
      }

      &.expanded {
        display: block;
        line-height: 1.5;
      }
    }

    // 工作经历描述文本样式
    .work-desc-text {
      &.collapsed {
        position: relative;
        line-height: 1.5;
        max-height: calc(1.5em * 2); // 限制为两行高度
        overflow: hidden;

        .view-detail {
          position: absolute;
          right: 0;
          bottom: 0;
          background: linear-gradient(to left, #fff 60%, transparent);
          padding-left: 30rpx;
          z-index: 2;
          line-height: 1.5;
        }
      }

      &.expanded {
        display: block;
        line-height: 1.5;
      }
    }

    // 项目经历描述文本样式
    .project-desc-text {
      &.collapsed {
        position: relative;
        line-height: 1.5;
        max-height: calc(1.5em * 2); // 限制为两行高度
        overflow: hidden;

        .view-detail {
          position: absolute;
          right: 0;
          bottom: 0;
          background: linear-gradient(to left, #fff 60%, transparent);
          padding-left: 30rpx;
          z-index: 2;
          line-height: 1.5;
        }
      }

      &.expanded {
        display: block;
        line-height: 1.5;
      }
    }
  }

  :deep(.wd-tooltip__inner) {
    direction: ltr;
    text-align: left;
    white-space: normal;
    font-size: 22rpx;
  }
}
</style>
