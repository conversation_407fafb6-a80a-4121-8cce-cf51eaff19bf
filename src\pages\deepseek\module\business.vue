<template>
  <z-paging ref="pagingRef" auto v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          safe-area-inset-top
          placeholder
          custom-class="!bg-transparent px-30rpx !lh-normal"
        >
          <template #left>
            <text class="c-#333333 text-48rpx font-500">电商运营</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>
    <view class="p-[44rpx_30rpx] flex flex-col gap-40rpx">
      <personal-list v-for="(item, key) in pageData" :key="key" />
    </view>
    <template #bottom>
      <customTabbar name="deepseek" />
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import customTabbar from '@/components/common/custom-tabbar.vue'
import personalList from '@/components/common/personal-list.vue'

defineOptions({
  name: 'DeepSeekBusiness',
})
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const themeVars: ConfigProviderThemeVars = {
  navbarHeight: '120rpx',
}

function queryList() {
  pagingRef.value.completeByNoMore([1, 2, 3, 4, 5, 6, 7, 8, 9], true)
}
</script>

<style lang="scss" scoped>
:deep(.wd-navbar__left) {
  align-items: end;
}
</style>
