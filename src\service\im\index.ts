import { POST } from '../index'
import { imDomainInfoInt, imCreateAccountInt, imCreateAccountDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 环信服务域名信息接口 */
export const imDomainInfo = (config?: HttpRequestConfig) =>
  POST<imDomainInfoInt>('/easyzhipin-api/im/imDomainInfo', {}, config)

/** 创建环信账号 */
export const imCreateAccount = (data: imCreateAccountDataInt, config?: HttpRequestConfig) =>
  POST<imCreateAccountInt>('/easyzhipin-api/im/createAccount', data, config)
