<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="verifiCode bg-img">
    <CustomNavBar></CustomNavBar>
    <view class="verifiCode-main">
      <view class="verifiCode-title">「输入手机验证码」</view>
      <view class="verifiCode-input">
        <yi-code
          style="margin: auto"
          :width="500"
          :maxlength="4"
          @onChange="getNum"
          @onComplete="submit"
        ></yi-code>
      </view>

      <view class="verifiCode-subTit">
        <view class="verifiCode-subTit-1">收不到验证码?</view>
      </view>
      <view class="verifiCode-time">
        <view class="flex-c" style="justify-content: center" v-if="isShow">
          <wd-count-down
            :auto-start="false"
            format="ss"
            :time="time"
            ref="countDown"
            @change="changeTime"
            custom-class="custom-class"
          />
          <text class="codeText">后重新获取</text>
        </view>
        <view class="verifiCode-subTit" v-if="!isShow">
          <view class="codeText" @click="getCode">重新获取</view>
        </view>
      </view>
      <!-- 	<view class="verifiCode-btn">
				<view class="btn_box" @click="submit">
					<view class="btn_bg">下一步</view>
				</view>
			</view>
		 -->
    </view>
    <common-link></common-link>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import CommonLink from '@/components/CommonLink/CommonLink.vue'
import yiCode from '@/uni_modules/yi-code/components/yi-code/yi-code.vue'
import { getLoginPhone, setToken, setInfo, setCheackInfo } from '@/utils/storege'
import { phonesendSms, subphoneLogin, newsInfo } from '@/interPost/login'
import { USER_TYPE } from '@/enum'

const { setUserIntel } = useUserInfo()
const { initEaseMobIM } = useEaseMobIM()

// 验证码
const smsCode = ref('')
// 协议
const isShow = ref(true)

const countDown = ref(null)
// 时间
const time = ref(60 * 1000)

// 获取验证
const getNum = (e: any) => {
  smsCode.value = e
}
// 修改时间变化处理
const changeTime = (t: any) => {
  // 当剩余时间为0时停止
  if (t.seconds === 0 && t.minutes === 0) {
    stopCountdown()
  }
}
onLoad(() => {
  getCode()
})
// 我要应聘
const setInit = async () => {
  const res: any = await newsInfo()
  if (res.code === 0) {
    setCheackInfo(res.data)
    // 是否可以使用
    if (res.data.userStatus === 1) {
      // 未选择应聘者和招聘者
      const step = res.data.completeStep
      const homeStep = [2, 3, 4, 5]
      // 定义步骤对应的跳转路径
      const stepRoutes = {
        '-999': { url: '/loginSetting/category/index', method: 'navigateTo' },
        0: { url: '/loginSetting/createResume/biographicalOne', method: 'navigateTo' },
        1: { url: '/loginSetting/category/JobIntention', method: 'navigateTo' },
        ...homeStep.reduce(
          (acc, step) => ({
            ...acc,
            [step]: { url: '/pages/home/<USER>', method: 'reLaunch' },
          }),
          {},
        ),
      }
      // 获取当前步骤的路由配置
      const route = stepRoutes[step]
      if (route) {
        if (homeStep.includes(step)) {
          await initEaseMobIM()
        }
        uni[route.method]({ url: route.url })
      }
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
// 我要招人
const setInitCompantInfo = async () => {
  const res: any = await newsInfo()
  if (res.code === 0) {
    setCheackInfo(res.data)
    // 是否可以使用
    if (res.data.userStatus === 1) {
      switch (res.data.completeStep) {
        case -999:
          uni.reLaunch({
            url: '/loginSetting/category/index',
          })
          break
        case 0:
          uni.reLaunch({
            url: '/loginSetting/companyJoin/companyInfo',
          })
          break
        case 1:
          uni.reLaunch({
            url: '/loginSetting/companyJoin/recrIdent',
          })
          break
        case 2:
          uni.reLaunch({
            url: '/loginSetting/companyJoin/jobCertificate',
          })
          break
        case 3:
          uni.reLaunch({
            url: '/pages/home/<USER>',
          })
          break
        case 4:
          uni.reLaunch({
            url: '/pages/home/<USER>',
          })
          break
        case 5:
          uni.reLaunch({
            url: '/pages/home/<USER>',
          })
          break
      }
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
const submit = async () => {
  if (smsCode.value.length !== 4) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  const res: any = await subphoneLogin({
    phone: getLoginPhone(),
    smsCode: smsCode.value,
  })
  console.log(res, '验证码登陆成功')
  if (res.code === 0) {
    setToken(res.data.token)
    setInfo(res.data)
    setUserIntel(res.data)
    if (Object.prototype.hasOwnProperty.call(res.data, 'type')) {
      if (res.data.lastLoginType === 0) {
        setInit()
      } else {
        setInitCompantInfo()
      }
    } else {
      setCheackInfo({})
      uni.reLaunch({
        url: '/loginSetting/category/index',
      })
    }
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
const getCode = async () => {
  try {
    isShow.value = true
    await nextTick()

    // 确保倒计时组件已挂载
    if (!countDown.value) {
      throw new Error('倒计时组件未加载')
    }

    // 重置并启动倒计时
    countDown.value.reset()
    countDown.value.start()

    // 发送验证码请求
    const res: any = await phonesendSms({
      captchaVerifyParam: null,
      imei: 'hfh38737',
      phone: getLoginPhone(),
    })

    if (res.code !== 0) {
      stopCountdown()
      uni.showToast({
        title: res.msg,
        icon: 'none',
      })
    }
  } catch (err) {
    stopCountdown()
    uni.showToast({
      title: '请求失败，请重试',
      icon: 'none',
    })
  }
}
// 停止倒计时的公共方法
const stopCountdown = () => {
  isShow.value = false
  if (countDown.value) {
    countDown.value.pause()
  }
}

onUnload(() => {
  stopCountdown()
})
</script>

<style lang="scss" scoped>
.codeText {
  font-size: 24rpx;
  color: $uni-color-primary;
}
.custom-class {
  font-size: 24rpx;
  color: $uni-color-primary;
}
.verifiCode {
  .verifiCode-main {
    padding-top: 240rpx;
    text-align: center;

    .verifiCode-title {
      font-size: #252525;
      font-size: 38rpx;
      font-weight: 500;
    }

    .verifiCode-input {
      width: 560rpx;
      margin: auto;
      margin-top: 80rpx;
    }

    .verifiCode-subTit {
      .verifiCode-subTit-1 {
        width: 220rpx;
        padding-top: 40rpx;
        margin: auto;
        font-size: 28rpx;
        color: #777777;
      }
    }

    .verifiCode-time {
      padding-top: 20rpx;
    }

    .verifiCode-btn {
      padding: 400rpx 44rpx 0rpx;
      text-align: center;

      .btn_box {
        box-sizing: border-box;
        width: 100%;
        padding: 0rpx 0rpx 0rpx;
        margin-top: 50rpx;

        .btn_bg {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 30rpx;
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
          border-radius: 14px 14px 14px 14px;
        }
      }
    }
  }
}
</style>
