<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <CustomNavBar title="项目经历">
      <template #left>
        <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
      </template>
    </CustomNavBar>
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">项目名称</view>
          <wd-input no-border v-model="fromData.projectName" placeholder="请输入项目名称" />
        </view>
        <view class="icon-right">
          <!-- <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon> -->
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">担任角色</view>
          <wd-input no-border v-model="fromData.takeOffice" placeholder="请输入担任角色" />
        </view>
        <view class="icon-right">
          <!-- <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon> -->
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">项目时间</view>
          <view class="flex-c">
            <wd-datetime-picker
              :zIndex="10001"
              type="year-month"
              v-model="fromData.startDate"
              placeholder="开始时间"
              class="flex-1"
              custom-value-class="custom-label-class"
            />
            <view class="w-20">-</view>
            <wd-datetime-picker
              :zIndex="10001"
              type="year-month"
              v-model="fromData.endDate"
              custom-value-class="custom-label-class"
              placeholder="结束时间"
              class="flex-1"
            />
          </view>
        </view>
        <view class="icon-right">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">项目描述</view>
          <view
            class="text-32rpx text-pre-wrap"
            :class="fromData.projectDescs ? 'c-#333333' : 'c-#888888'"
            @click="goWorkDescription"
          >
            {{ fromData.projectDescs ? fromData.projectDescs : '项目描述' }}
          </view>
        </view>
        <view class="icon-right" @click="goWorkDescription">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">项目业绩</view>
          <view
            class="text-32rpx text-pre-wrap"
            :class="fromData.projectPerformance ? 'c-#333333' : 'c-#888888'"
            @click="goWorkPerformance"
          >
            {{ fromData.projectPerformance ? fromData.projectPerformance : '项目业绩' }}
          </view>
          <!-- <view class="color-8 text-32rpx">非必填</view> -->
        </view>
        <view class="icon-right" @click="goWorkPerformance">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">项目链接</view>
          <wd-input no-border v-model="fromData.projectLinkAddr" placeholder="请输入项目链接" />
        </view>
        <view class="icon-right">
          <!-- <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon> -->
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed flex-c">
        <view
          v-if="isAdd === 'edit'"
          class="btn-delet m-r-30rpx"
          @click="del"
          :class="isAdd === 'edit' ? 'w-30' : ''"
        >
          删除
        </view>
        <view class="btn_box" :class="isAdd === 'edit' ? 'w-70' : 'w-100'">
          <view class="btn_bg" @click="submit">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>
<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import isEqual from 'lodash/isEqual'
import { useResumeStore } from '@/store'
import { resumeProjectAdd, resumeProjectDel, resumeProjectUpdate } from '@/interPost/resume'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const resumeStore = useResumeStore()
const objItem = ref(null)
const isAdd = ref(null)
const fromData = ref({
  baseInfoId: null,
  endDate: '',
  id: '',
  projectDescs: '',
  projectLinkAddr: '',
  projectName: '',
  projectPerformance: '',
  startDate: '',
  takeOffice: '',
})
// 表单初始化
const fromDataInit = ref(JSON.parse(JSON.stringify(fromData.value)))
onLoad((options) => {
  // id
  fromData.value.baseInfoId = options.id
  fromDataInit.value.baseInfoId = options.id
  // 是否是新增
  isAdd.value = options.isAdd
  // 编辑的数据
  if (isAdd.value === 'edit') {
    objItem.value = JSON.parse(decodeURIComponent(options.item))
    fromData.value = JSON.parse(decodeURIComponent(options.item))
  }
  // 是否有
})
onShow(() => {
  if (isAdd.value === 'add') {
    fromData.value.projectDescs = resumeStore.projectDescs
    fromData.value.projectPerformance = resumeStore.projectPerformance
  } else {
    fromData.value.projectDescs = resumeStore.projectDescs
      ? resumeStore.projectDescs
      : objItem.value.projectDescs
    fromData.value.projectPerformance = resumeStore.projectPerformance
      ? resumeStore.projectPerformance
      : objItem.value.projectPerformance
  }
})
// 项目描述
const goWorkDescription = () => {
  uni.navigateTo({
    url: '/resumeRelated/projectExperience/workContent?projectDescs=' + fromData.value.projectDescs,
  })
}
// 业绩
const goWorkPerformance = () => {
  uni.navigateTo({
    url:
      '/resumeRelated/projectExperience/workPerformance?projectPerformance=' +
      fromData.value.projectPerformance,
  })
}
// 删除
const del = async () => {
  uni.showModal({
    title: '提示',
    content: '您确定要删除吗?',
    // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
    success: function (res) {
      if (res.confirm) {
        resumeProjectDel({ id: objItem.value.id }).then((res: any) => {
          if (res.code === 0) {
            resumeStore.setProjectDescs('')
            resumeStore.setProjectPerformance('')
            uni.navigateBack()
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none',
              duration: 3000,
            })
          }
        })
      }
    },
  })
}
// 返回
const back = () => {
  if (isAdd.value === 'add') {
    if (isEqual(fromData.value, fromDataInit.value)) {
      resumeStore.setProjectDescs('')
      resumeStore.setProjectPerformance('')
      uni.navigateBack()
    } else {
      uni.showModal({
        title: '提示',
        content: '您有内容未提交保存,确认返回吗?',
        // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
        success: function (res) {
          if (res.confirm) {
            resumeStore.setProjectDescs('')
            resumeStore.setProjectPerformance('')
            uni.navigateBack()
          }
        },
      })
    }
  } else {
    console.log('000000')
    if (
      objItem.value.baseInfoId === fromData.value.baseInfoId &&
      objItem.value.endDate === fromData.value.endDate &&
      objItem.value.id === fromData.value.id &&
      objItem.value.projectDescs === fromData.value.projectDescs &&
      objItem.value.projectLinkAddr === fromData.value.projectLinkAddr &&
      objItem.value.projectName === fromData.value.projectName &&
      objItem.value.projectPerformance === fromData.value.projectPerformance &&
      objItem.value.startDate === fromData.value.startDate &&
      objItem.value.takeOffice === fromData.value.takeOffice
    ) {
      resumeStore.setProjectDescs('')
      resumeStore.setProjectPerformance('')
      uni.navigateBack()
    } else {
      uni.showModal({
        title: '提示',
        content: '您有内容未提交保存,确认返回吗?',
        // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
        success: function (res) {
          if (res.confirm) {
            resumeStore.setProjectDescs('')
            resumeStore.setProjectPerformance('')
            uni.navigateBack()
          }
        },
      })
    }
  }
}
// 提交
const submit = async () => {
  if (isAdd.value === 'add') {
    const res: any = await resumeProjectAdd(fromData.value)
    if (res.code === 0) {
      resumeStore.setProjectDescs('')
      resumeStore.setProjectPerformance('')
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  } else {
    const res: any = await resumeProjectUpdate(fromData.value)
    if (res.code === 0) {
      resumeStore.setProjectDescs('')
      resumeStore.setProjectPerformance('')
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
::v-deep .wd-input__placeholder {
  font-size: 32rpx !important;
  color: #888888;
}
::v-deep .wd-picker__placeholder {
  font-size: 32rpx;
  color: #888888;
}
::v-deep .wd-input__inner {
  font-size: 32rpx !important;
  font-weight: 500;
}
::v-deep .wd-picker__cell {
  width: 100% !important;
  padding-left: 0rpx !important;
  background: transparent !important;
}
::v-deep .wd-picker__arrow {
  display: none;
}
::v-deep .uni-input-input {
  font-size: 32rpx;
  color: #333;
}
::v-deep .custom-label-class {
  view {
    font-size: 32rpx !important;
  }
}
.w-100 {
  width: 100%;
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn-delet {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 30%;
    padding: 20rpx 0rpx;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background: #959595;
    border-radius: 14px 14px 14px 14px;
  }
  .btn_box {
    box-sizing: border-box;
    // width: 70%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.pageContaner {
  padding: 20rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
</style>
