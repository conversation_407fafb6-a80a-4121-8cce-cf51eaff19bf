<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="权限管理"></CustomNavBar>
    <view class="setting">
      <view class="setting-list flex-between">
        <view class="setting-left">
          <view class="setting-left-name">位置权限</view>
        </view>
        <wd-switch
          custom-class="swich-btn"
          v-model="value3"
          active-color="#fff"
          inactive-color="#ff"
        />
      </view>
      <view class="setting-list flex-between">
        <view class="setting-left">
          <view class="setting-left-name">日历权限</view>
        </view>
        <wd-switch
          custom-class="swich-btn"
          v-model="value3"
          active-color="#fff"
          inactive-color="#ff"
        />
      </view>
      <view class="setting-list flex-between">
        <view class="setting-left">
          <view class="setting-left-name">相机权限</view>
        </view>
        <wd-switch
          custom-class="swich-btn"
          v-model="value3"
          active-color="#fff"
          inactive-color="#ff"
        />
      </view>
      <view class="setting-list flex-between">
        <view class="setting-left">
          <view class="setting-left-name">麦克风权限</view>
        </view>
        <wd-switch
          custom-class="swich-btn"
          v-model="value3"
          active-color="#fff"
          inactive-color="#ff"
        />
      </view>
      <view class="setting-list flex-between">
        <view class="setting-left">
          <view class="setting-left-name">蓝牙权限</view>
        </view>
        <wd-switch
          custom-class="swich-btn"
          v-model="value3"
          active-color="#fff"
          inactive-color="#ff"
        />
      </view>
      <view class="setting-list flex-between">
        <view class="setting-left">
          <view class="setting-left-name">相册权限</view>
        </view>
        <wd-switch
          custom-class="swich-btn"
          v-model="value3"
          active-color="#fff"
          inactive-color="#ff"
        />
      </view>
      <view class="setting-list flex-between">
        <view class="setting-left">
          <view class="setting-left-name">通知权限</view>
        </view>
        <wd-switch
          custom-class="swich-btn"
          v-model="value3"
          active-color="#fff"
          inactive-color="#ff"
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const value3 = ref(true)
</script>
<style scoped lang="scss">
::v-deep .wd-switch__circle {
  width: 46rpx;
  height: 32rpx;
  margin-top: 6rpx;
  background-color: #3e9cff !important;
  border-radius: 20rpx !important;
}
.setting {
  padding: 0rpx 40rpx;
}

.setting-list {
  padding: 40rpx 0rpx;
  border-bottom: 2rpx solid #d7d6d6 !important;

  .setting-left {
    .setting-left-name {
      font-size: 32rpx;
      font-weight: 500;
      color: #333333;
    }
  }
}
.swich-btn {
  width: 120rpx;
  height: 50rpx;
  line-height: 50rpx;
}
::v-deep .wd-switch__circle {
  background-color: red;
}
.line-h {
  width: 100%;
  height: 10rpx;
  background-color: #e8e8e8;
}

.ts-text {
  font-size: 22rpx;
  color: #333;
}

.setting-text {
  font-size: 22rpx;
  color: #888888;
}
</style>
