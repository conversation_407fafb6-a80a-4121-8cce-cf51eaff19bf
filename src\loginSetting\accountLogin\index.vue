<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="accountLogin bg-img">
    <CustomNavBar></CustomNavBar>
    <view class="accountLogin-main">
      <view class="accountLogin-name">账号</view>
      <view class="login-input">
        <wd-input no-border v-model="userName" placeholder="请输入账号" />
      </view>
      <view class="accountLogin-password">密码</view>
      <view class="login-input">
        <wd-input show-password no-border v-model="password" placeholder="请输入密码" />
      </view>
      <view class="accountLogin-btn">
        <view class="btn_box">
          <view class="btn_bg">登陆</view>
        </view>
      </view>
      <view class="login-ohter">
        <wd-divider>其他登录选项</wd-divider>
        <view class="login-ohter-icon">
          <view class="t-icon t-icon-weixin"></view>
          <view class="t-icon t-icon-QQ"></view>
          <view class="t-icon t-icon-zhifubaozhifu"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const isText = ref(true)
const userName = ref('<EMAIL>')
const password = ref('zanshnh')
</script>

<style lang="scss">
::v-deep .wd-checkbox__label {
  margin-left: 4rpx !important;
}
::v-deep .wd-input {
  width: 100%;
  background-color: transparent !important;
}
::v-deep .wd-input__placeholder {
  font-size: 32rpx !important;
}
::v-deep .wd-input__inner {
  font-size: 40rpx !important;
  font-weight: 500;
  color: #18181b !important;
}
::v-deep .wd-input__icon {
  background-color: transparent !important;
}
.t-icon {
  display: inline-block !important;
  width: 50rpx;
  height: 50rpx;
  margin-right: 40rpx;
}
.login-input {
  padding: 20rpx 0rpx !important;
  border-bottom: 1rpx solid #d6d6d6;
}
.btn_box {
  box-sizing: border-box;
  width: 100%;
  margin-top: 50rpx;

  .btn_bg {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 30rpx;
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
    border-radius: 14px 14px 14px 14px;
  }
}
.accountLogin {
  .accountLogin-main {
    padding: 60rpx 44rpx 0rpx;

    .accountLogin-name {
      font-size: 32rpx;
      color: $uni-text-color-assis;
    }

    .accountLogin-password {
      padding-top: 68rpx;
    }

    .accountLogin-btn {
      padding-top: 120rpx;
    }

    .login-ohter {
      padding: 48rpx 140rpx 0rpx;
      color: $uni-text-color-assis;

      .login-ohter-icon {
        padding-top: 40rpx;
        text-align: center;
      }
    }
  }
}
</style>
