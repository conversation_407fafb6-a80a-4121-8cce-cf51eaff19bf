/**
 * 处理流式数据并提取纯文本内容
 * @param {string} streamData 流式数据字符串
 * @returns {string} 提取后的纯文本内容
 */
export function extractTextFromStream(streamData) {
  // 1. 按行分割数据
  const lines = streamData.split('\n')

  let resultText = ''

  // 2. 处理每一行
  lines.forEach((line) => {
    // 跳过空行和[DONE]标记
    if (!line.trim() || line.includes('[DONE]')) return

    // 3. 提取"data:"后面的内容
    if (line.startsWith('data:')) {
      const content = line.substring(5).trim()
      if (content) {
        resultText += content
      }
    }
  })

  return resultText
}
