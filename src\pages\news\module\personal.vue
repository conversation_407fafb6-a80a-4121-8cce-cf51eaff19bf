<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar :fixed="false">
        <template #left>
          <view class="page_flex_left">聊天</view>
        </template>
        <template #right>
          <image class="chartIcon" src="/static/img/Group_1171275033.png" @click="goInfo"></image>
        </template>
        <template #content>
          <view class="content_flex">
            <view class="content_search">
              <view class="content_search_bg">
                <view class="content_search_left">
                  <image
                    src="/static/images/home/<USER>"
                    mode="aspectFill"
                    class="_img"
                  ></image>
                </view>
                <view class="content_search_right">
                  <wd-input no-border placeholder="搜索您想要的内容" v-model="keyword"></wd-input>
                </view>
              </view>
            </view>
          </view>
          <view class="p-b-10rpx">
            <view class="tabList flex-c m-l-40rpx">
              <view
                class="tabList-item m-r-40rpx"
                v-for="(item, index) in tabsWithBadge"
                :key="index"
                :class="tabWithBadge === index ? 'active' : 'nomal'"
                @click="changeActive(index)"
              >
                <view class="text-28rpx">{{ item.title }}</view>
                <view class="tabDot">2</view>
              </view>
            </view>
          </view>
        </template>
      </CustomNavBar>
    </template>
    <!-- 聊天列表 -->
    <view class="px-40rpx pb-20rpx">
      <newsPersonalList @chat="handleChat" />
    </view>
    <template #bottom>
      <custom-tabbar name="news" />
    </template>
  </z-paging>
  <wd-toast />
  <wd-message-box />
</template>

<script setup lang="ts">
import { CommonUtil, useMessage } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBarBig.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import newsPersonalList from '@/components/news/news-personal-list.vue'

defineOptions({
  name: 'NewsPersonal',
})
const message = useMessage()
const { userRoleIsRealName } = useUserInfo()
const { pagingRef, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 140deg, #FFC8C8 0%, #EDF1FF 22%, #FFFFFF 100%)',
  },
})
const keyword = ref('')
const tabWithBadge = ref(0)
const tabsWithBadge = ref([
  {
    title: '全部',
    badgeProps: {
      modelValue: 10,
      right: '-8px',
    },
  },
  {
    title: '新招呼',
    badgeProps: {
      modelValue: 100,
      max: 99,
      right: '-8px',
    },
  },
  {
    title: '仅沟通',
    badgeProps: {
      isDot: true,
      right: '-8px',
      showZero: true,
    },
  },
  {
    title: '有交换',
    badgeProps: {
      modelValue: 100,
      max: 99,
      right: '-8px',
    },
  },
])
// 消息
const goInfo = () => {
  uni.navigateTo({
    url: '/chartPage/message/index',
  })
}
// tabbar切换
const changeActive = (index) => {
  tabWithBadge.value = index
}
const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}

const handleChat = async (id: string) => {
  try {
    await userToRealName()
    uni.navigateTo({
      url: CommonUtil.buildUrlWithParams('/ChatUIKit/modules/Chat/index', {
        type: 'singleChat',
        id,
      }),
    })
  } catch (error) {}
}
</script>

<style lang="scss" scoped>
.custom-class {
  background-color: transparent !important;
}
.active {
  border-bottom: 8rpx solid rgba(255, 167, 167, 1);
  border-radius: 4rpx;
}
.nomal {
  border-bottom: 8rpx solid transparent;
}
.tabList-item {
  position: relative;
  margin-right: 60rpx;
}
.tabDot {
  position: absolute;
  top: 6rpx;
  right: -30rpx;
  width: 28rpx;
  height: 28rpx;
  font-size: 22rpx;
  line-height: 28rpx;
  color: #fff;
  background: rgba(204, 204, 204, 1);
  border-radius: 50%;
}
.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 20rpx 40rpx 40rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;

      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 10rpx 30rpx;
      color: #fff;
      background: #fff;
      border-radius: 80rpx;
      // box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        width: 10%;
        // width: 40rpx;
        height: 40rpx;
        ._img {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.chartIcon {
  width: 50rpx;
  height: 50rpx;
}
.activeStyle {
  font-size: 30rpx;
  font-weight: 500;
}

.page_flex_left {
  position: relative;
  padding-top: 20rpx;
  padding-bottom: 10rpx;
  font-size: 60rpx;
  font-weight: 600;
  color: #000000;
}

.page_flex_left::after {
  position: absolute;
  bottom: 10rpx;
  left: 0rpx;
  width: 170rpx;
  height: 12rpx;
  content: '';
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 32rpx;
}

.u-searchs {
  margin-top: 30rpx;
}

.tabList {
  padding: 30rpx 0rpx;
}
</style>
