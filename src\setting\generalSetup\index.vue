<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="通用设置"></CustomNavBar>
    <view class="setting">
      <view class="setting-list border-b">
        <view class="flex-between">
          <view class="text-32rpx font-w-500">储存空间管理</view>
          <view class="subText flex-c">
            <view class="subText">清理缓存301.32MB</view>
            <wd-icon
              name="chevron-right"
              size="20px"
              color="#888888"
              class="arrow-right-icon"
            ></wd-icon>
          </view>
        </view>
        <view class="subText">手机可用空间74.32GB，易直聘占有手机不足1%储存空间</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
const goAccountNumber = () => {
  uni.navigateTo({
    url: '/setting/accountNumber/index',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}

::v-deep .cell-set .u-line {
  border-bottom: 2rpx solid #d7d6d6 !important;
}

::v-deep .u-cell__title-text {
  font-size: 32rpx !important;
  font-weight: 500;
  color: #333;
}

::v-deep .u-cell__body--large {
  padding: 40rpx 0rpx 10rpx;
}

::v-deep .u-cell__value {
  font-size: 22rpx;
  color: #888888;
}

::v-deep .u-cell__label--large {
  font-size: 22rpx;
}

::v-deep .u-cell--clickable {
  background-color: transparent !important;
}

.setting-text {
  font-size: 22rpx;
  color: #888888;
}
</style>
