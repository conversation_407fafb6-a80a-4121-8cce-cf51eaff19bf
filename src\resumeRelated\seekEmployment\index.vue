<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="求职期望">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="form-list-item mainText">求职类型</view>
        <view class="flex-1 flex-c flex-c-just">
          <view class="flex-1 text-r">
            <wd-radio-group v-model="jobType" shape="dot" inline checked-color="#FF8686">
              <wd-radio :value="item.value" v-for="(item, index) in radiolist1" :key="index">
                {{ item.name }}
              </wd-radio>
            </wd-radio-group>
          </view>
          <!-- <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon> -->
        </view>
      </view>
      <view class="form-list flex-between" @click="hanRegion">
        <view class="form-list-item mainText font-w-500">工作地点</view>
        <view class="flex-1 flex-c flex-c-just">
          <view class="page_input1-text" :class="provinceName ? 'selelctColor' : 'nomalColor'">
            {{ provinceName ? provinceName + cityNameShow + districtName : '请选择期望地区' }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between" @click="handCareer">
        <view class="form-list-item mainText font-w-500">期望职位</view>
        <view class="flex-1 flex-c flex-c-just">
          <view
            class="page_input1-text"
            :class="positionObj.expectedPositions ? 'selelctColor' : 'nomalColor'"
          >
            {{ positionObj.expectedPositions ? positionObj.expectedPositions : '请选择期望职位' }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>

      <view class="form-list flex-between">
        <view class="form-list-item mainText font-w-500">期望薪资</view>
        <view class="flex-1 flex-c flex-c-just">
          <wd-picker
            ref="pickerPop"
            :columns="salaryColumns"
            label=""
            v-model="salaryValue"
            :column-change="onSalaryColumnChange"
            :display-format="salaryDisplayFormat"
            @confirm="handleSalaryConfirm"
            custom-class="custom-class"
          />
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between" @click="handPosition">
        <view class="form-list-item mainText font-w-500">期望行业</view>
        <view class="flex-1 flex-c flex-c-just">
          <view class="page_input1-text" :class="jobName ? 'selelctColor' : 'nomalColor'">
            {{ jobName ? jobName : '请选择期望行业' }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed flex-c">
        <view
          v-if="isAdd === 'edit'"
          class="btn-delet m-r-30rpx"
          @click="delSubmit"
          :class="isAdd === 'edit' ? 'w-30' : ''"
        >
          删除
        </view>
        <view class="btn_box" :class="isAdd === 'edit' ? 'w-70' : 'w-100'">
          <view class="btn_bg" @click="submit">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>
<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import {
  userJobIntentionAdd,
  userJobIntentionUpdate,
  userJobIntentionDel,
} from '@/interPost/resume'
import { useLoginStore, useResumeStore } from '@/store'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const resumeStore = useResumeStore()
// vuex数据
const loginStore = useLoginStore()
const salaryValue = ref([0, 0])
const isAdd = ref('')
// 工作编辑
const objItem = ref()
// 显示城市的字段
const cityNameShow = ref('')

// 数据响应式
const jobType = ref(1) // 工作类型
const id = ref(null) // 简历id
const cityObj = ref<AnyObject>({}) // 城市
const positionObj = ref<AnyObject>({}) // 职位
const jobObj = ref([]) // 工作
const salary = ref([]) // 薪资
const salaryExpectationStart = ref<any>(0) // 薪资开始
const salaryExpectationEnd = ref<any>(0) // 薪资结束
const provinceName = ref('') // 省份
const provinceCode = ref('')
const cityName = ref('') // 市
const cityCode = ref('')
const districtName = ref('') // 区
const districtCode = ref('')
const industry = ref('') // 行业
const industryId = ref(null)
const jobName = ref('')
const pickerShow = ref(false) // 控制picker显示隐藏
const columnsNum = ref(2) // 设置为两列
const selectedProvinceIndex = ref(0) // 默认选中的省份索引
const selectIndex = ref(0)
const pickerPop = ref()

// 基本案列数据
const radiolist1 = ref([
  {
    name: '全职',
    value: 1,
    disabled: false,
  },
  {
    name: '兼职',
    value: 2,
    disabled: false,
  },
])
// 返回
const back = () => {
  loginStore.setCity({})
  loginStore.setpositionData({})
  loginStore.setjobObjOnline({})
  uni.navigateBack()

  // uni.navigateBack()
  // loginStore.setCity({})
  // loginStore.setpositionData({})
  // loginStore.setjobArry([])
  // if (resumeStore.isAdd === 'add') {
  //   console.log(isEqual(fromData.value, fromDataInit.value))
  //   if (isEqual(fromData.value, fromDataInit.value)) {
  //     loginStore.setCity({})
  //     loginStore.setpositionData({})
  //     loginStore.setjobArry([])
  //     uni.navigateBack()
  //   } else {
  //     uni.showModal({
  //       title: '提示',
  //       content: '您有内容未提交保存,确认返回吗?',
  //       // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
  //       success: function (res) {
  //         if (res.confirm) {
  //           loginStore.setCity({})
  //           loginStore.setpositionData({})
  //           loginStore.setjobArry([])
  //           uni.navigateBack()
  //         }
  //       },
  //     })
  //   }
  // }
}
onUnload(() => {
  console.log('页面卸载===')
  // 城市
  loginStore.setCity({})
  // 职位
  loginStore.setpositionData({})
  // 职位
  loginStore.setjobArry([])
})
// 修改薪资数据
const salaryData = {
  面议: ['面议'], // 面议单独处理
  '3k': ['5k', '8k', '10k'],
  '5k': ['8k', '10k', '15k'],
  '8k': ['10k', '15k', '20k'],
  '10k': ['15k', '20k', '25k'],
  '15k': ['20k', '25k', '30k'],
  '20k': ['25k', '30k', '35k'],
  '25k': ['30k', '35k', '40k'],
  '30k': ['35k', '40k', '45k'],
}
// 删除
const delSubmit = async () => {
  uni.showModal({
    title: '提示',
    content: '您确定要删除该条信息吗?',
    success: function (res) {
      if (res.confirm) {
        userJobIntentionDel({ id: objItem.value.id }).then((res: any) => {
          if (res.code === 0) {
            loginStore.setCity({})
            loginStore.setpositionData({})
            loginStore.setjobObjOnline({})
            resumeStore.setIsRefresh(1)
            uni.navigateBack()
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none',
              duration: 3000,
            })
          }
        })
      }
    },
  })
}
// 期望地区
const hanRegion = () => {
  uni.navigateTo({
    url: '/loginSetting/category/region',
  })
}
// 期待职位
const handCareer = () => {
  uni.navigateTo({
    url: '/loginSetting/category/career',
  })
}
// 期待行业
const handPosition = () => {
  uni.navigateTo({
    url: '/resumeRelated/resumeIndustry/index',
  })
}
// 获取期待职位
const submit = async () => {
  if (!provinceName.value) {
    uni.showToast({
      title: '请选择期望地区',
      icon: 'none',
      duration: 3000,
    })
    return
  }

  if (!positionObj.value.expectedPositions) {
    uni.showToast({
      title: '请选择期望职业',
      icon: 'none',
      duration: 3000,
    })
    return
  }

  if (isAdd.value === 'add') {
    const res: any = await userJobIntentionAdd({
      // 省
      provinceName: cityObj.value.provinceName,
      provinceCode: cityObj.value.provinceCode,
      //  城市编
      cityCode: cityObj.value.cityCode,
      cityName: cityObj.value.cityName,
      // 区域
      districtCode: cityObj.value.districtCode,
      districtName: cityObj.value.districtName,
      salaryExpectationStart: salaryExpectationStart.value,
      salaryExpectationEnd: salaryExpectationEnd.value,
      jobType: jobType.value,
      baseInfoId: id.value,
      industry: industry.value,
      industryId: industryId.value,
      expectedPositions: positionObj.value.expectedPositions,
      expectedPositionsCode: positionObj.value.expectedPositionsCode,
    })
    if (res.code === 0) {
      loginStore.setCity({})
      loginStore.setpositionData({})
      loginStore.setjobObjOnline({})
      resumeStore.setIsRefresh(1)
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  } else {
    const res: any = await userJobIntentionUpdate({
      // 省
      provinceName: provinceName.value,
      provinceCode: provinceCode.value,
      //  城市编
      cityCode: cityCode.value,
      cityName: cityName.value,
      // 区域
      districtCode: districtCode.value,
      districtName: districtName.value,
      salaryExpectationStart: salaryExpectationStart.value,
      salaryExpectationEnd: salaryExpectationEnd.value,
      jobType: jobType.value,
      baseInfoId: id.value,
      id: objItem.value.id,
      // 行业
      industry: industry.value,
      industryId: industryId.value,
      // 职位
      expectedPositions: positionObj.value.expectedPositions,
      expectedPositionsCode: positionObj.value.expectedPositionsCode,
    })
    if (res.code === 0) {
      loginStore.setCity({})
      loginStore.setpositionData({})
      loginStore.setjobObjOnline({})
      resumeStore.setIsRefresh(1)
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
onLoad((options) => {
  console.log(options, 'options==')
  // 简历id
  id.value = options.id
  isAdd.value = options.isAdd

  if (isAdd.value === 'edit') {
    objItem.value = JSON.parse(decodeURIComponent(options.item))
    jobType.value = objItem.value.jobType
    // provinceName + cityName  + expectedCity
    // provinceName.value = objItem.value.provinceName
    // console.log(provinceName.value)
    // salaryValue.value =
    //   objItem.value.salaryExpectationStart === 0
    //     ? [0, 0]
    //     : [objItem.value.salaryExpectationStart, objItem.value.salaryExpectationEnd]
    // jobName.value = objItem.value.industry
    // console.log(objItem.value, 'objItem.value===编辑')
  }
})
onShow(() => {
  // console.log(isAdd.value, 'isAdd.value==')
  if (isAdd.value === 'add') {
    // 区域
    cityObj.value = loginStore.cityObj
    // 省
    provinceName.value = cityObj.value.provinceName
      ? cityObj.value.provinceName
      : provinceName.value
    // 省code expectedCity
    provinceCode.value = cityObj.value.provinceCode
      ? cityObj.value.provinceCode
      : provinceCode.value
    // 市
    cityName.value = cityObj.value.cityName ? cityObj.value.cityName : cityName.value
    // 市code
    cityCode.value = cityObj.value.cityCode ? cityObj.value.cityCode : cityCode.value
    console.log(provinceCode.value, cityName.value, '===============')
    if (provinceName.value === cityName.value) {
      cityNameShow.value = ''
    } else {
      cityNameShow.value = cityObj.value.cityName ? cityObj.value.cityName : cityName.value
    }
    // 区
    districtName.value = cityObj.value.districtName
      ? cityObj.value.districtName
      : districtName.value
    // 区code
    districtCode.value = cityObj.value.districtCode
      ? cityObj.value.districtCode
      : districtCode.value

    // 职位cityName
    positionObj.value = loginStore.positionObj

    // 行业
    const jobList = loginStore.jobObjOnline
    // console.log(jobList, 'jobList===============')
    if (jobList?.industry) {
      // console.log('进入了=================', jobList)
      industry.value = loginStore.jobObjOnline.industry
      industryId.value = loginStore.jobObjOnline.industryId
      jobName.value = loginStore.jobObjOnline.industry
    } else {
      // 默认的行业
      industryId.value = 0
      jobName.value = '不限'
      industry.value = '不限'
      loginStore.setjobObjOnline({
        industry: industry.value,
        industryId: Number(industryId.value),
      })
    }
  } else {
    // 区域
    cityObj.value = loginStore.cityObj
    // 省
    provinceName.value = cityObj.value.provinceName
      ? cityObj.value.provinceName
      : objItem.value.provinceName
    // 省code expectedCity
    provinceCode.value = cityObj.value.provinceCode
      ? cityObj.value.provinceCode
      : objItem.value.provinceCode
    // 市
    cityName.value = cityObj.value.cityName ? cityObj.value.cityName : objItem.value.cityName
    // 市code
    cityCode.value = cityObj.value.cityCode ? cityObj.value.cityCode : objItem.value.cityCode
    if (provinceName.value === cityName.value) {
      cityNameShow.value = ''
    } else {
      cityNameShow.value = cityObj.value.cityName ? cityObj.value.cityName : objItem.value.cityName
    }
    // 区
    if (cityObj.value.districtName) {
      districtName.value = cityObj.value.districtName
    } else if (!cityObj.value.districtName && cityObj.value.cityName) {
      districtName.value = ''
    } else {
      districtName.value = objItem.value.districtName
    }
    if (cityObj.value.districtCode) {
      districtCode.value = cityObj.value.districtCode
    } else if (!cityObj.value.districtCode && cityObj.value.cityCode) {
      districtCode.value = ''
    } else {
      districtCode.value = objItem.value.districtCode
    }

    // 显示图标
    const city = {
      //  省份编码
      provinceName: provinceName.value,
      provinceCode: Number(provinceCode.value),
      //  城市编
      cityCode: Number(cityCode.value),
      cityName: cityName.value,
      // 区域
      districtCode: Number(districtCode.value),
      districtName: districtName.value,
    }

    loginStore.setCity(city)
    // 职位cityName
    const obj = {
      expectedPositions: objItem.value.expectedPositions,
      expectedPositionsCode: Number(objItem.value.expectedPositionsCode),
    }
    // console.log(obj, loginStore.positionObj, 'obj')
    positionObj.value = loginStore.positionObj?.expectedPositions ? loginStore.positionObj : obj
    // console.log(positionObj.value, 'positionObj.value===')
    loginStore.setpositionData(positionObj.value)

    // 行业industry
    const jobList = loginStore.jobObjOnline
    if (jobList.industry) {
      industry.value = loginStore.jobObjOnline.industry
      industryId.value = loginStore.jobObjOnline.industryId
      jobName.value = loginStore.jobObjOnline.industry
      loginStore.setjobObjOnline({
        industry: industry.value,
        industryId: Number(industryId.value),
      })
    } else {
      industry.value = objItem.value.expectedIndustry
      industryId.value = objItem.value.expectedIndustryCode
      jobName.value = objItem.value.expectedIndustry
      loginStore.setjobObjOnline({
        industry: industry.value,
        industryId: Number(industryId.value),
      })
    }
  }
})
const salaryColumns = ref([
  Object.keys(salaryData).map((item) => ({ label: item, value: item })),
  salaryData['面议'].map((item) => ({ label: item, value: item })),
])

// 修改列变化处理
const onSalaryColumnChange = (picker, values, columnIndex, resolve) => {
  if (columnIndex === 0) {
    const selected = values[0]?.value || '面议'
    if (selected === '面议') {
      picker.setColumnData(1, [{ label: '面议', value: '面议' }])
    } else {
      picker.setColumnData(
        1,
        salaryData[selected].map((item: any) => ({ label: item, value: item })),
      )
    }
    resolve()
  }
}

const salaryDisplayFormat = (items: any) => {
  return items.map((item) => item.label).join('-')
}

const handleSalaryConfirm = ({ value }) => {
  if (value[0].indexOf('k') !== -1) {
    salaryExpectationStart.value = value[0].replace('k', '000') // 薪资开始
    salaryExpectationEnd.value = value[1].replace('k', '000') // 薪资结束
  } else {
    salaryExpectationStart.value = 0
    salaryExpectationEnd.value = 0
  }
}

const handMoney = () => {
  pickerPop.value.open()
}
</script>

<style lang="scss" scoped>
::v-deep .wd-radio-group {
  padding-right: 0rpx !important;
  background-color: transparent;
}
::v-deep .wd-picker__arrow,
.wd-picker__clear {
  display: none;
}
.selelctColor {
  color: #333333;
}

.nomalColor {
  color: #888888;
}
.page_input1-text {
  font-size: 32rpx;
}
::v-deep .wd-picker__cell {
  padding-right: 0rpx !important;
  background: transparent;
}
::v-deep .wd-picker__value {
  margin-right: 0rpx;
  font-size: 32rpx;
}
::v-deep .wd-radio__label {
  font-size: 32rpx;
  color: #333;
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn-delet {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 30%;
    padding: 20rpx 0rpx;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background: #959595;
    border-radius: 14px 14px 14px 14px;
  }
  .btn_box {
    box-sizing: border-box;
    // width: 70%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
.pageContaner {
  padding: 20rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      width: 160rpx;
      // color: #0f0f0f;
    }

    .arrow-right-icon {
      display: flex;
      justify-content: right;
      width: 40rpx;
    }
  }
}
</style>
