<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar :title="isShow === 'sys' ? '系统消息详情' : '活动消息详情'"></CustomNavBar>
    </template>
    <view class="contanner-page">
      <view class="text-32rpx c-#333">
        {{ infoObj.title }}
      </view>
      <view class="c-#888 text-24rpx">
        {{ infoObj.createTime }}
      </view>
      <view class="line-20 text-26rpx c-#555">
        {{ infoObj.content }}
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryDetailById, queryActivitiListById } from '@/interPost/messege'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// id
const id = ref(null)
const isShow = ref(null)
const infoObj = ref({})
// 获取列表
const queryList = async () => {
  if (isShow.value === 'sys') {
    const res: any = await queryDetailById({ id: id.value })
    if (res.code === 0) {
      infoObj.value = res.data
    }
  } else {
    const res: any = await queryActivitiListById({ id: id.value, messageType: 1 })
    if (res.code === 0) {
      infoObj.value = res.data
    }
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  id.value = options.id
  isShow.value = options.isShow
  queryList()
})
</script>

<style lang="scss" scoped>
.contanner-page {
  padding: 20rpx;
  margin: 40rpx;
  background: #fff;
  border-radius: 10rpx;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}
</style>
