/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-pages

interface NavigateToOptions {
  url: "/pages/guide/index" |
       "/pages/deepseek/index" |
       "/pages/home/<USER>" |
       "/pages/login/index" |
       "/pages/mine/index" |
       "/pages/news/index" |
       "/loginSetting/accountLogin/index" |
       "/loginSetting/category/career" |
       "/loginSetting/category/expPosition" |
       "/loginSetting/category/index" |
       "/loginSetting/category/JobIntention" |
       "/loginSetting/category/region" |
       "/loginSetting/companyJoin/companyInfo" |
       "/loginSetting/companyJoin/jobCertificate" |
       "/loginSetting/companyJoin/recrIdent" |
       "/loginSetting/createResume/biographicalOne" |
       "/loginSetting/createResume/biographicalTwo" |
       "/loginSetting/Externalfiling/index" |
       "/loginSetting/regPage/index" |
       "/loginSetting/verifiCode/index" |
       "/chartPage/chartPage/index" |
       "/chartPage/message/index" |
       "/chartPage/message/infoDetail" |
       "/chartPage/message/messageActiveList" |
       "/chartPage/message/messageSysList" |
       "/resumeRelated/AttachmentResume/GeneratePDF copy" |
       "/resumeRelated/AttachmentResume/GeneratePDF" |
       "/resumeRelated/AttachmentResume/index copy" |
       "/resumeRelated/AttachmentResume/index" |
       "/resumeRelated/AttachmentResume/WebViewpdf" |
       "/resumeRelated/collect/index" |
       "/resumeRelated/collectResume/index" |
       "/resumeRelated/communicate/index" |
       "/resumeRelated/company/index copy" |
       "/resumeRelated/company/index" |
       "/resumeRelated/corporateName/index copy" |
       "/resumeRelated/corporateName/index" |
       "/resumeRelated/CustomerService/index" |
       "/resumeRelated/education/index" |
       "/resumeRelated/education/school" |
       "/resumeRelated/filter/index copy" |
       "/resumeRelated/filter/index" |
       "/resumeRelated/HomeRegion/index" |
       "/resumeRelated/interview/index" |
       "/resumeRelated/interview/WaitMeeting" |
       "/resumeRelated/jobDetail/hrJobDetail" |
       "/resumeRelated/jobDetail/index" |
       "/resumeRelated/jobExpectations/index" |
       "/resumeRelated/moreJobs/index" |
       "/resumeRelated/mySkill/index" |
       "/resumeRelated/onlineResume/index" |
       "/resumeRelated/preview/index" |
       "/resumeRelated/projectExperience/index" |
       "/resumeRelated/projectExperience/workContent" |
       "/resumeRelated/projectExperience/workPerformance" |
       "/resumeRelated/recruiter/index" |
       "/resumeRelated/resumeIndustry/index" |
       "/resumeRelated/salaryWork/index" |
       "/resumeRelated/seekEmployment/index" |
       "/resumeRelated/violationDis/index" |
       "/resumeRelated/workExperience/dept" |
       "/resumeRelated/workExperience/index" |
       "/resumeRelated/workExperience/workContent" |
       "/resumeRelated/workExperience/workPerformance" |
       "/resumeRelated/workExperience/workSkills" |
       "/resumeRelated/onlineResume/certificate/index" |
       "/resumeRelated/onlineResume/myAdvantage/index" |
       "/resumeRelated/onlineResume/portfolio/index" |
       "/resumeRelated/onlineResume/qualiCertificate/index" |
       "/setting/accountMange/index" |
       "/setting/accountNumber/index" |
       "/setting/AdressMange/index" |
       "/setting/certificateImage/businesslicense" |
       "/setting/certificateImage/humanResources" |
       "/setting/certificateImage/TelServices" |
       "/setting/generalSetup/index" |
       "/setting/identityAuth/index" |
       "/setting/IdentitySwitching/index" |
       "/setting/loginDevice/index" |
       "/setting/notice/index" |
       "/setting/permission/index" |
       "/setting/personalInfo/index copy" |
       "/setting/personalInfo/index" |
       "/setting/phoneUpdata/index" |
       "/setting/PrivacyAgreement/CancelAgreement" |
       "/setting/PrivacyAgreement/CommAgreement" |
       "/setting/PrivacyAgreement/HumanResources" |
       "/setting/PrivacyAgreement/index" |
       "/setting/PrivacyAgreement/PrivacyPolicy" |
       "/setting/PrivacyAgreement/ResumeUser" |
       "/setting/PrivacyAgreement/UserAgreement" |
       "/setting/setting/index" |
       "/setting/wxUpdata/index" |
       "/paymentRelated/positionPay/index" |
       "/ChatUIKit/modules/Chat/index" |
       "/ChatUIKit/modules/VideoPreview/index";
}
interface RedirectToOptions extends NavigateToOptions {}

interface SwitchTabOptions {
  url: "/pages/home/<USER>" | "/pages/deepseek/index" | "/pages/news/index" | "/pages/mine/index"
}

type ReLaunchOptions = NavigateToOptions | SwitchTabOptions;

declare interface Uni {
  navigateTo(options: UniNamespace.NavigateToOptions & NavigateToOptions): void;
  redirectTo(options: UniNamespace.RedirectToOptions & RedirectToOptions): void;
  switchTab(options: UniNamespace.SwitchTabOptions & SwitchTabOptions): void;
  reLaunch(options: UniNamespace.ReLaunchOptions & ReLaunchOptions): void;
}
