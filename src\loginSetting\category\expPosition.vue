<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="containner bg-img">
    <CustomNavBar title="期望行业">
      <template #right>
        <view class="qr-btn" @click="submitPage">确定</view>
      </template>
    </CustomNavBar>
    <view class="u-searchs">
      <view class="content_flex">
        <view class="content_search">
          <view class="content_search_bg">
            <view class="content_search_left">
              <image src="/static/img/sousuo-2_1 (3).png" mode="aspectFill"></image>
            </view>
            <view class="content_search_right">
              <wd-input no-border placeholder="搜索您想要的内容" v-model="keyword"></wd-input>
            </view>
          </view>
        </view>
      </view>

      <view class="containner-select-list">
        <view
          class="tag-select myStyle-box text-28rpx"
          v-for="(item, index) in selectedItems"
          :key="index"
          @click="toggleSelect(item)"
        >
          {{ item.name }}
        </view>
      </view>
    </view>

    <!-- 主列表容器 -->
    <view class="page-list">
      <!-- 左侧父级分类 -->
      <scroll-view
        scroll-y
        :style="{
          height:
            selectedItems.length > 0
              ? 'calc(100vh - ' + (scrollViewHeight + 30) + 'px)'
              : 'calc(100vh - ' + scrollViewHeight + 'px)',
        }"
        class="page-list-left"
      >
        <view v-for="(parent, pIndex) in list" :key="pIndex">
          <view
            :class="[
              activeFIndex === pIndex ? 'activeBg' : 'normalBg',
              pIndex === activeFIndex - 1 ? 'prev-selected' : '',
              pIndex === activeFIndex + 1 ? 'next-selected' : '',
              'page-list-left-text',
            ]"
            class="page-list-left-text"
            @click="activeF(pIndex)"
          >
            {{ parent.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 右侧子级及孙子级内容 -->
      <scroll-view
        scroll-y
        :style="{
          height:
            selectedItems.length > 0
              ? 'calc(100vh - ' + (scrollViewHeight + 30) + 'px)'
              : 'calc(100vh - ' + scrollViewHeight + 'px)',
        }"
        class="page-list-right"
      >
        <view>
          <view class="page-list-right-p">
            <view class="page-tag-list">
              <view
                v-for="(grandchild, cIndex) in currentChildren"
                :key="cIndex"
                class="tag-select-r"
                :class="grandchild.active ? 'myStyle-box' : ''"
                @click="changeActive(cIndex)"
              >
                {{ grandchild.name }}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import industryDataList from '@/utils/json/industry.json'
import { getCustomBar } from '@/utils/storege'
import { useLoginStore } from '@/store'
const loginStore = useLoginStore()
const keyword = ref('')
const activeFIndex = ref(0)
const list = ref([])
const scrollViewHeight = ref(0)

const currentChildren = computed(() => {
  return list.value[activeFIndex.value]?.childerIndustryData || []
})

const selectedItems = computed({
  get() {
    const items = []
    list.value.forEach((parent) => {
      parent.childerIndustryData &&
        parent.childerIndustryData.forEach((grandchild) => {
          if (grandchild.active) {
            items.push(grandchild)
          }
        })
    })
    return items
  },
  set(value) {
    // 清空所有选中状态
    list.value.forEach((parent) => {
      parent.childerIndustryData &&
        parent.childerIndustryData.forEach((child) => {
          child.active = false
        })
    })

    // 同步新选中的状态
    value.length &&
      value.forEach((item) => {
        item.active = true
      })
  },
})

// 提交
const submitPage = () => {
  loginStore.setjobArry(selectedItems.value)
  uni.navigateBack()
}

const activeF = (index) => {
  activeFIndex.value = index
}

const changeActive = (cIndex) => {
  const target = list.value[activeFIndex.value].childerIndustryData[cIndex]

  if (selectedItems.value.length === 1 && selectedItems.value[0].code === 0) {
    uni.showToast({
      title: '您已选择不限行业了',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 如果是取消选中直接操作
  if (target.active) {
    target.active = false
    return
  }

  // 检查已选数量
  if (selectedItems.value.length >= 3) {
    uni.showToast({
      title: '最多只能选择3个行业',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 特殊处理行业分类
  if (list.value[activeFIndex.value].code === 0) {
    const jobList = [
      {
        name: list.value[activeFIndex.value].childerIndustryData[cIndex].name,
        code: list.value[activeFIndex.value].childerIndustryData[cIndex].code,
      },
    ]
    loginStore.setjobArry(jobList)
    uni.navigateBack()
  } else {
    target.active = true
  }
}

const toggleSelect = (item) => {
  item.active = !item.active
}

onLoad(async () => {
  await nextTick()
  scrollViewHeight.value = getCustomBar() + 75
  const industryList = industryDataList.industryData
  industryList.forEach((parent) => {
    parent.childerIndustryData &&
      parent.childerIndustryData.forEach((child: AnyObject) => {
        child.active = false
      })
  })
  list.value = industryList
  selectedItems.value = loginStore.jobObj
})
</script>

<style lang="scss">
/* 新增顶部标签样式 */
::v-deep .wd-input {
  width: 100%;
  background-color: transparent !important;
}
::v-deep .wd-input__placeholder {
  font-size: 28rpx !important;
  color: #fff !important;
}
::v-deep .wd-input__inner {
  font-size: 28rpx !important;
  font-weight: 500;
  color: #fff !important;
}
.qr-btn {
  width: 100rpx;
  padding: 5rpx 0rpx;
  font-size: 28rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 10rpx;
}

.containner-select-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  padding: 20rpx 0;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 40rpx 0rpx;
  padding-bottom: 20rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 30rpx;
      color: #fff;
      background: rgba(61, 61, 61, 0.34);
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 10%;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.myStyle-box::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('../img/Mask_group(2).png');
}

.page-list-right-title {
  padding-bottom: 25rpx;
  font-weight: bold;
}

.page-list-right-p {
  padding: 30rpx 20rpx 0rpx !important;
}

.activeBg {
  font-weight: 500;
  color: #1160ff;
  background: transparent;
}

.tag-select {
  position: relative;
  width: 200rpx;
  padding: 8rpx 0;
  color: #555;
  text-align: center;
  background-color: #fff;
  border: 1px solid #1160ff;
  border-radius: 10rpx;
}

.normalBg {
  background: #e8e8e8;
}

.u-searchs {
  padding: 0 46rpx;
  border-bottom: 1rpx solid $uni-border-b-color;
}

.page-list {
  display: flex;
  padding: 0;

  &-left {
    width: 230rpx;

    &-text {
      padding: 30rpx 10rpx 30rpx 10rpx;
      font-size: 28rpx;
      text-align: center;
      transition: all 0.3s;

      // 相邻元素的圆角
      &.prev-selected {
        border-bottom-right-radius: 20rpx;
      }

      &.next-selected {
        border-top-right-radius: 20rpx;
      }
    }
  }

  &-right {
    flex: 1;

    &-p {
      padding: 40rpx 30rpx;

      &-title {
        margin-bottom: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }
}

.page-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
  justify-content: space-between;

  .tag-select-r {
    position: relative;
    width: 225rpx;
    padding: 12rpx 4rpx !important;
    margin-bottom: 10rpx;
    font-size: 28rpx;
    color: #333;
    text-align: center;
    background: #f5f5f5;
    border-radius: 10rpx;
    transition: all 0.3s;

    &.myStyle-box {
      border: 1px solid #1160ff;
    }
  }
}
</style>
