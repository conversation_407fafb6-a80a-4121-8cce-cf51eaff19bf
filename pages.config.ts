import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: '易直聘',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  preloadRule: {},
  tabBar: {
    custom: true,
    color: '#999999',
    selectedColor: '#018d71',
    list: [
      {
        pagePath: 'pages/home/<USER>',
      },
      {
        pagePath: 'pages/deepseek/index',
      },
      {
        pagePath: 'pages/news/index',
      },
      {
        pagePath: 'pages/mine/index',
      },
    ],
  },
  subPackages: [
    {
      root: 'ChatUIKit',
      pages: [
        {
          path: 'modules/Chat/index',
          style: {
            navigationStyle: 'custom',
            // #ifdef MP-WEIXIN
            disableScroll: true,
            // #endif
            'app-plus': {
              bounce: 'none',
              softinputNavBar: 'none',
            },
          },
        },
        {
          path: 'modules/VideoPreview/index',
          style: {
            navigationBarTitleText: 'Video Preview',
            'app-plus': {
              bounce: 'none',
            },
          },
        },
      ],
    },
  ],
})
