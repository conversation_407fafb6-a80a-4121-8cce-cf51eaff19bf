<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useBaseUrlList } from '@/hooks/common/useBaseUrlList'
import { setNesInfo } from '@/hooks/common/useBaseNewInfo'

const { baseUrlMsgListIntApi } = useBaseUrlList()
const { initEaseMobIM } = useEaseMobIM()

onLaunch(async () => {
  console.log('App Launch')
  baseUrlMsgListIntApi()
  initEaseMobIM()
})
onShow(async () => {
  console.log('App Show=====')
  // await setNesInfo()
})
onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* 每个页面公共css */
@import 'style/reset';
@import 'style/common';
@import 'style/iconfont-weapp-icon.css';
// @import 'components/gaoyia-parse/parse.css';
</style>
