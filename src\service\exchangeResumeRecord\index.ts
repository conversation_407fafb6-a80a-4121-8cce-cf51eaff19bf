import { POST } from '../index'
import {
  sendResumeDataInt,
  batchSendResumeDataInt,
  batchSendResumeInt,
  batchSendResumeCallbackDataInt,
} from './types'
import { HttpRequestConfig } from 'luch-request'

/** C端用户发送简历(只记录不发IM)接口 */
export const exchangeResumeRecordSendResume = (
  data: sendResumeDataInt,
  config?: HttpRequestConfig,
) => POST<number>('/easyzhipin-api/exchangeResumeRecord/sendResume', data, config)

/** 批量发送简历只落库不发送IM消息接口 */
export const exchangeResumeRecordBatchSendResume = (
  data: batchSendResumeDataInt,
  config?: HttpRequestConfig,
) => POST<batchSendResumeInt>('/easyzhipin-api/exchangeResumeRecord/batchSendResume', data, config)

/** 批量发送简历回调接口 */
export const exchangeResumeRecordBatchSendResumeCallback = (
  data: batchSendResumeCallbackDataInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/exchangeResumeRecord/batchSendResumeCallback', data, config)
