<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" v-model="pageData" @query="queryList" :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="我的收藏"></CustomNavBar>
      <view class="p-t-40rpx">
        <view class="tabbar-list flex-c">
          <view
            class="tabbar-item"
            v-for="(item, index) in tabbarlist"
            :key="index"
            @click="selectItem(index)"
          >
            <view class="tabbar-name" :class="index == activeIndex ? 'activeItem' : 'normalItem'">
              {{ item.name }}
            </view>
          </view>
        </view>
      </view>
    </template>

    <view class="onlineRes" v-if="activeIndex == 0">
      <view
        class="my-jl-card m-b-20rpx"
        v-for="(item, index) in pageData"
        :key="index"
        @click="goCompany(item.id)"
      >
        <view class="flex-between line-18">
          <view class="collect-conpany p-b-10rpx">{{ item.name }}</view>
          <view class="flex-c" style="justify-content: right; width: 180rpx">
            <!-- <u-icon name="map" color="#888888"></u-icon> -->
            <!-- <wd-icon name="location" size="12" color="#888888"></wd-icon> -->
            <view class="subText">{{ item.sizeName }}</view>
          </view>
        </view>
        <!-- <view class="card-name-subname subText p-b-10rpx">{{ item.sizeName }}</view> -->
        <view class="my-jl-card-content subText u-line-2">
          {{ item.profile }}
        </view>
      </view>
    </view>
    <view class="onlineRes" v-if="activeIndex == 1">
      <view
        class="my-jl-card m-b-20rpx"
        v-for="(item, index) in pageData"
        :key="index"
        @click="goDetail(item.id, item.companyId)"
      >
        <view class="flex-between">
          <view class="flex-1">
            <view class="collect-conpany">{{ item.positionName }}</view>
            <view class="card-name-subname subText line-18">{{ item.sizeName }}</view>
          </view>
          <view class="c-#888 text-r text-26rpx salary" style="width: 180rpx">
            {{ item.workSalaryBegin }}-{{ item.workSalaryEnd }}
          </view>
        </view>
        <view class="c-#888 text-24rpx">{{ item.name }}</view>
        <view class="my-jl-card-right flex-c" v-if="item.positionKey?.length > 0">
          <view
            class="my-jl-card-right-btn flex-c"
            v-for="(subName, index) in item.positionKey"
            :key="index"
          >
            <view class="subText m-l-10rpx">
              {{ subName }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryCollectCompanyList, queryCollectPositionList } from '@/interPost/my'
import { numberTokw } from '@/utils/common'
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const list1 = ref([{ name: '本科' }, { name: '1-3年' }])

const list2 = ref([{ name: '英语4-6级' }, { name: '某某资格证书' }])

const activeIndex = ref(0)
const value = ref('')

const tabbarlist = ref([{ name: '收藏的公司' }, { name: '收藏的岗位' }])

const list = ref([
  { name: '某某资格证书', pic: '/static/my/Group_1171274971.png' },
  { name: '某某资格证书', pic: '/static/my/Group_1171274971.png' },
  { name: '某某资格证书', pic: '/static/my/Group_1171274971.png' },
  { name: '某某资格证书', pic: '/static/my/Group_1171274971.png' },
])
const params = reactive({
  entity: {},
  orderBy: {},
  size: pageInfo.pageSize,
  page: pageInfo.pageNum,
})
onShow(async () => {
  await uni.$onLaunched
  pagingRef.value.reload()
})
const companyList = async () => {
  const res: any = await queryCollectCompanyList(params)
  if (res.code === 0) {
    pagingRef.value.complete(res.data.list)
  }
}
const goCompany = (id) => {
  console.log(id)
  uni.navigateTo({
    url: `/resumeRelated/company/index?companyId=${id}`,
  })
}
const goDetail = (id: any, companyId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId}`,
  })
}
const positionList = async () => {
  const res: any = await queryCollectPositionList(params)
  if (res.code === 0) {
    res.data?.list &&
      res.data.list.forEach((ele: any) => {
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryBegin =
          ele.workSalaryBegin === 0 ? '面议' : numberTokw(ele.workSalaryBegin + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '面议' : numberTokw(ele.workSalaryEnd + '')
      })

    pagingRef.value.complete(res.data.list)
  }
}
const queryList = async () => {
  if (activeIndex.value === 0) {
    await companyList()
  } else {
    await positionList()
  }
}
const selectItem = async (index) => {
  activeIndex.value = index
  pagingRef.value.reload()
  if (index === 0) {
    await companyList()
  } else {
    await positionList()
  }
}
</script>

<style scoped lang="scss">
.CustomNavBar-right {
  color: #888888;
}
.salary {
  color: #ff8080 !important;
}
.collect-conpany {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  white-space: normal;
}

.activeColor {
  background-color: #d7dfff !important;
}

.activeText {
  color: #4075ff !important;
}

.my-jl-card {
  padding: 30rpx 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

  .my-jl-card-left {
    justify-content: flex-start;
    width: 50%;

    .my-jl-card-left-header {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
    }

    .my-jl-card-left-item {
      margin-left: 10rpx;

      .card-name {
        font-weight: 500;
        color: #555;
      }
    }
  }

  .my-jl-card-right {
    flex: 1;
    flex-wrap: wrap;

    .my-jl-card-right-btn {
      padding: 2rpx 40rpx;
      margin: 10rpx 10rpx 10rpx 0rpx;
      background-color: #f3f3f3;
      border-radius: 10rpx;

      .pic {
        width: 20rpx;
        height: 20rpx;
      }
    }
  }

  .my-jl-card-cunstrct {
    line-height: 52rpx;

    .my-jl-card-cunstrct-img {
      width: 28rpx;
      height: 28rpx;
    }
  }
}

.tabbar-list {
  justify-content: center;
  width: 500rpx;
  padding: 10rpx 0rpx 10rpx;
  margin: auto;
  background-image: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 20rpx;

  .tabbar-item {
    padding: 0rpx 20rpx;
    font-size: 28rpx;
    text-align: center;
  }

  .normalItem {
    font-weight: 500;
    color: #000000;
  }

  .activeItem {
    width: 240rpx;
    padding: 4rpx 10rpx;
    font-weight: 500;
    color: #1573fe;
    background-color: #fff;
    border-radius: 10rpx;
  }
}

.onlineRes-rz {
  margin-left: 30rpx;
}

.name-rz {
  margin-right: 4rpx;
  font-size: 26rpx;
  color: #ff0000;
}

.onlineRes-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.onlineRes-subtitle {
  font-size: 24rpx;
  color: #888888;
}

.work-qw-line {
  line-height: 50rpx;
}

.onlineRes {
  padding: 40rpx;
}
</style>
