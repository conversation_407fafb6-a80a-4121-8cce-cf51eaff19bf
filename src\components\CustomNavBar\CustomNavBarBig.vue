<template>
  <view
    class="customNavbar"
    :style="
      fixed
        ? {
            background: bgColor,
            position: 'fixed',
            top: '0',
            left: '0',
            'z-index': 9999,
            backgroundImage: bgImg,
          }
        : {}
    "
  >
    <!-- 系统导航栏 -->
    <view class="system" :style="{ height: `${statusBar * 2}rpx` }"></view>
    <view class="navbar" :style="{ height: `${(customBar - statusBar) * 2}rpx` }">
      <view class="left">
        <!-- 通过具名插槽来自定义导航栏左侧内通 -->
        <slot name="left"></slot>
      </view>

      <!-- 通过具名插槽来自定义导航栏右侧内通 -->
      <view class="right">
        <slot name="right"></slot>
      </view>
    </view>
    <view class="content">
      <!-- 通过具名插槽来自定义导航栏左侧内通 -->
      <slot name="content"></slot>
    </view>
    <!-- 占位视图防止内容被遮挡 -->
    <!-- 		<view
			:style="{ height: `${(customBar + tabHeight) * 2}rpx` }"
			style="background-color: red"
		></view> -->
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { setCustomBar, setStatusBar } from '@/utils/storege'
const props = defineProps({
  tabHeight: {
    type: Number,
    default: 0,
  },
  bgImg: {
    type: String,
    default: '',
  },
  // 自定义左侧文字
  leftText: {
    type: String,
    default: '',
  },
  // 标题名称
  title: {
    type: String,
    default: '',
  },
  // 自定义右侧文字
  rightText: {
    type: String,
    default: '',
  },
  // 背景颜色
  bgColor: {
    type: String,
    default: '',
  },
  fixed: {
    type: Boolean,
    default: true,
  },
})

const statusBar = ref(0) // 状态栏
const customBar = ref(0) // 状态栏 + 导航栏高度
const platform = ref('') // 平台
const isPageUp = ref(true) // 是否返回上一页（微信小程序）

onLoad(() => {
  /*
    这里通过 uni.getSystemInfo 获取系统信息
    用来计算系统导航栏高度和导航栏高度
  */
  uni.getSystemInfo({
    success: (res) => {
      platform.value = res.platform
      statusBar.value = res.statusBarHeight
      customBar.value = res.statusBarHeight + 60
      // 这里是在安卓手机上加上 3 像素（当时好像是在安卓水滴屏上，系统导航栏高度较低才加上去的，大家可以真机自己调试一下）
      if (res.platform === 'android') {
        statusBar.value += 3
        customBar.value += 3
      }
      setCustomBar(customBar.value)
      setStatusBar(statusBar.value)
    },
  })

  // getCurrentPages 官方解释用于获取当前页面的实例
  const pages = getCurrentPages()
  if (pages[pages.length - 2]) {
    isPageUp.value = true
  } else {
    isPageUp.value = false
  }
})

// 返回上一页面
const back = () => {
  console.log('返回上一页！')
  uni.navigateBack()
}

// 微信小程序返回首页
const homePage = () => {
  console.log('返回首页！')
  uni.switchTab({
    url: '/pages/home/<USER>',
  })
}

// 暴露方法给父组件
defineExpose({
  back,
  homePage,
})
</script>

<style lang="scss" scoped>
.customNavbar {
  .system {
    width: 100vw;
    background: transparent;
  }

  .navbar {
    position: relative;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100vw;
    padding: 0 20rpx 0rpx 40rpx;
  }

  .left {
    width: calc(100% - 200rpx);
  }

  .right {
    display: flex;
    justify-content: right;
    width: 200rpx;
    text-align: right;
  }

  .left {
    text-align: left;
  }

  .content {
    position: relative;
    box-sizing: border-box;
    width: 100%;
    text-align: center;
  }
}
</style>
