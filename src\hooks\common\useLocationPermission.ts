import { ref, computed, onMounted } from 'vue'
import { permissionManager } from '@/utils/permission'
import type { PermissionResult } from '@/types/permission'

export interface LocationPermissionState {
  // 权限状态
  hasPermission: boolean
  // 系统定位服务状态
  systemLocationEnabled: boolean
  // 检查中状态
  checking: boolean
  // 错误信息
  error: string | null
}
// 状态管理
const state = ref<LocationPermissionState>({
  hasPermission: false,
  systemLocationEnabled: false,
  checking: false,
  error: null,
})
export function useLocationPermission() {
  // 计算属性：是否可以使用定位
  const canUseLocation = computed(
    () => state.value.hasPermission && state.value.systemLocationEnabled,
  )
  /**
   * 检查位置权限
   */
  const checkLocationPermission = async (): Promise<PermissionResult> => {
    state.value.checking = true
    state.value.error = null

    try {
      const result = await permissionManager.checkPermission('location')
      state.value.hasPermission = result.granted

      if (result.message) {
        state.value.error = result.message
      }

      return result
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '检查位置权限失败'
      state.value.error = errorMessage
      console.error('[useLocationPermission] 检查位置权限失败:', error)

      return {
        granted: false,
        status: -1,
        message: errorMessage,
      }
    } finally {
      state.value.checking = false
    }
  }

  /**
   * 检查系统定位服务
   */
  const checkSystemLocation = (): boolean => {
    try {
      const enabled = permissionManager.checkSystemLocationEnabled()
      state.value.systemLocationEnabled = enabled
      return enabled
    } catch (error) {
      console.error('[useLocationPermission] 检查系统定位服务失败:', error)
      state.value.error = '检查系统定位服务失败'
      return false
    }
  }

  /**
   * 完整的位置权限检查（权限 + 系统服务）
   */
  const checkFullLocationAccess = async (): Promise<{
    canUse: boolean
    permissionResult: PermissionResult
    systemEnabled: boolean
  }> => {
    const permissionResult = await checkLocationPermission()
    const systemEnabled = checkSystemLocation()

    return {
      canUse: permissionResult.granted && systemEnabled,
      permissionResult,
      systemEnabled,
    }
  }

  /**
   * 请求位置权限（如果未授权，会引导用户到设置页面）
   */
  const requestLocationPermission = async (): Promise<boolean> => {
    const result = await checkLocationPermission()

    if (!result.granted) {
      // 权限被拒绝，询问是否跳转到设置
      const confirmResult = await new Promise<boolean>((resolve) => {
        uni.showModal({
          title: '位置权限申请',
          content: '应用需要位置权限来提供相关服务，请前往设置页面开启位置权限',
          confirmText: '去设置',
          cancelText: '取消',
          success: (res) => {
            resolve(res.confirm)
          },
          fail: () => {
            resolve(false)
          },
        })
      })

      if (confirmResult) {
        permissionManager.gotoAppPermissionSetting()
      }
    }

    return result.granted
  }

  /**
   * 检查并请求完整的位置访问权限
   */
  const ensureLocationAccess = async (): Promise<boolean> => {
    const { canUse, systemEnabled } = await checkFullLocationAccess()

    if (!canUse) {
      if (!state.value.hasPermission) {
        // 没有权限，请求权限
        await requestLocationPermission()
      } else if (!systemEnabled) {
        // 有权限但系统定位服务未开启
        uni.showModal({
          title: '定位服务未开启',
          content: '请在手机设置中开启定位服务',
          showCancel: false,
          confirmText: '知道了',
        })
      }
    }

    return canUse
  }

  /**
   * 获取当前位置（封装了权限检查）
   */
  const getCurrentLocation = async (): Promise<UniApp.GetLocationSuccess> => {
    const hasAccess = await ensureLocationAccess()
    if (!hasAccess) {
      throw new Error('没有位置访问权限')
    }
    return new Promise<UniApp.GetLocationSuccess>((resolve, reject) => {
      uni.getLocation({
        success: (res) => resolve(res),
        fail: (error) => {
          console.error('[useLocationPermission] 获取位置失败:', error)
          reject(new Error(error?.errMsg || '获取位置失败'))
        },
      })
    })
  }
  return {
    state: computed(() => state.value),
    canUseLocation,
    checkLocationPermission,
    checkSystemLocation,
    checkFullLocationAccess,
    requestLocationPermission,
    ensureLocationAccess,
    getCurrentLocation,
    gotoSettings: () => permissionManager.gotoAppPermissionSetting(),
  }
}
