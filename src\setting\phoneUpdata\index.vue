<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="手机号"></CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="flex-c border-b p-b-40rpx p-t-40rpx">
        <view class="text-30rpx m-r-10rpx w-40 font-500">手机号</view>
        <wd-input v-model="phone" no-border placeholder="请输入手机号"></wd-input>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed" v-if="!phoneReg">
        <view class="btn_box" @click="submit">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { updatePhone } from '@/interPost/my'
import { regPhone } from '@/utils/rule'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// 用戶名
const phone = ref('')
const phoneReg = ref('')

onLoad((options) => {
  phone.value = options.phone
  phoneReg.value = options.phone
})

const submit = async () => {
  // 手机号
  if (!regPhone.test(phone.value)) {
    uni.showToast({
      title: '请输入正确手机号',
      icon: 'none',
    })
    return
  }
  const res: any = await updatePhone({ phone: phone.value })
  if (res.code === 0) {
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  background-color: transparent;
}

.labelName {
  width: 200rpx;
  text-align: left;
}
.position-r {
  position: absolute;
  top: 10rpx;
  right: 0rpx;
}

.pageContaner {
  padding: 0rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
