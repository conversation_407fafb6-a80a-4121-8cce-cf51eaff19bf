import { POST, POSTPaging } from '@/service'

// c端用户我的-通知查询接口
export const noticeList = () => {
  return POST('/easyzhipin-api/noticeMessage/userNotice')
}
// c端用户我消息外列表接口(系统通知 1 职位通知 2 道具通知 3)
export const queryDetailList = (data) => {
  return POSTPaging('/easyzhipin-api/noticeMessage/queryDetailList', data)
}
// c端用户活动消息外列表接口(新消息/旧消息)
export const queryActivitiList = (data) => {
  return POSTPaging('/easyzhipin-api/noticeMessage/queryActivityList', data)
}
// 详情
export const queryDetailById = (data) => {
  return POST('/easyzhipin-api/noticeMessage/queryDetailById', data)
}
// 活动详情
export const queryActivitiListById = (data) => {
  return POST('/easyzhipin-api/noticeMessage/queryActivityById', data)
}
