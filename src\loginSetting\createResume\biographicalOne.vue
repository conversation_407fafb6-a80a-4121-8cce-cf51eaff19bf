<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar>
      <template #right>
        <wd-img :src="identitySwitchingImg" width="45rpx" height="45rpx" @click="changeIdentFun" />
      </template>
    </CustomNavBar>
    <!-- <CustomNavBar /> -->
    <view class="page_box">
      <view class="page_padding">
        <view class="page_flex_row">
          <view class="page_flex">
            <view class="page_flex_left">身份创建</view>
            <view class="page_flex_icon" :style="{ top: statusBarHeight * 2 + 'rpx' }">
              <view class="page_flex_img"></view>
            </view>
          </view>
          <view class="page_input text-wrap">
            在线简历将会向BOSS展示,我们会妥善保护你的隐私,后续你也可以在设置中将简历隐藏～
          </view>
        </view>
        <view class="page_flex_row_bottom">
          <view class="tag-name">姓名</view>
          <view class="input-border">
            <!-- <up--input fontSize="32rpx" color="#000" placeholder="请输入你的真实姓名" border="surround" v-model="trueName"></up--input> -->
            <wd-input
              type="text"
              :no-border="true"
              v-model="trueName"
              placeholder="请输入你的真实姓名"
            />
          </view>
        </view>
        <view class="page_flex_row_bottom">
          <view class="tag-name">性别</view>
          <view class="tag-select-r-list">
            <view
              @click="changeactive(index, item)"
              class="tag-select-r"
              :class="
                index === activeIndex && sex === 1
                  ? 'myStyle-box'
                  : index === activeIndex && sex === 2
                    ? 'myStyle-box-pink'
                    : 'tag-select-r-normal'
              "
              v-for="(item, index) in sexList"
              :key="index"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
        <view class="page_flex_row_bottom">
          <view class="tag-name">出生年月</view>
          <view class="month-picker">
            <!--  :fields="fieldsType" @change="handlePickerChange" -->
            <month-picker
              mode="date"
              :value="birthday"
              class="picker"
              @change="handlePickerChange"
            ></month-picker>
          </view>
        </view>
      </view>
    </view>

    <view class="btn_fixed" @click="submit">
      <view class="btn_box">
        <view class="btn_bg">下一步</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import monthPicker from '@/components/month-picker/month-picker.vue'
import { resumeBaseInfoList } from '@/interPost/biographical'
import { getStatusBar, getInfo, getCheackInfo } from '@/utils/storege'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
const { changeIdent } = useChangeIdent()

const trueName = ref('')
const birthday = ref('')
const sex = ref(1)
const activeIndex = ref(0)
const innerValue = ref('')
const statusBarHeight = ref(0)
const value = ref('')
const sexList = reactive([
  {
    name: '男',
    value: 1,
  },
  {
    name: '女',
    value: 2,
  },
])
// 切换身份
const changeIdentFun = async () => {
  changeIdent()
}
onLoad(() => {
  statusBarHeight.value = getStatusBar()
})
onShow(() => {
  getInfoList()
})
const getInfoList = async () => {
  await uni.$onLaunched
  // console.log(getCheackInfo().baseInfoId, 'setCheackInfo().baseinfoid')
  const res: any = await resumeBaseInfoList({
    id: getCheackInfo().baseInfoId,
  })
  // console.log(res, 'res====查到的', getCheackInfo().baseInfoId)
  if (res.code === 0) {
    if (Object.prototype.hasOwnProperty.call(res, 'data')) {
      activeIndex.value = res.data.sex === 1 ? 0 : 1
      sex.value = res.data.sex
      trueName.value = res.data.trueName
      birthday.value = res.data.birthday.substring(0, 7)
    }
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
const submit = () => {
  if (trueName.value === '') {
    uni.showToast({
      title: '请填写真实姓名',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (birthday.value === '') {
    uni.showToast({
      title: '请选择出生年月',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  uni.navigateTo({
    url: `/loginSetting/createResume/biographicalTwo?trueName=${trueName.value}&sex=${sex.value}&birthday=${birthday.value}`,
  })
}
const handlePickerChange = (e: any) => {
  birthday.value = e
  console.log(e, birthday.value, 'birthday.value')
}
const changeactive = (index: any, item: any) => {
  activeIndex.value = index
  sex.value = item.value
}
</script>

<style lang="scss" scoped>
::v-deep .u-input__content__field-wrapper__field {
  height: 70rpx !important;
}

::v-deep .u-line-progress__line {
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%) !important;
}
.input-border {
  padding: 25rpx 20rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 10rpx;
}
.month-picker {
  padding: 0rpx 0rpx;
}

.page_flex_left::after {
  position: absolute;
  bottom: 15rpx;
  left: 0rpx;
  width: 200rpx;
  height: 8rpx;
  content: '';
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 32rpx;
}
.myStyle-box-pink {
  background: rgba(253, 239, 239, 1);
  border: 1rpx solid rgba(255, 190, 190, 1);
}
.btn_fixed {
  position: fixed;
  bottom: 80rpx;
  width: 100%;

  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 50rpx;
    padding-bottom: 0;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.page_flex_for {
  display: flex;
  flex-direction: row;

  .page_flex_list {
    display: flex;
    flex-direction: row;
    margin-left: 20rpx;

    .page_select_border {
      padding: 0 22rpx;
      font-size: 22rpx;
      line-height: 44rpx;
      color: #3e9cff;
      background: #f1f1ff;
      border: 1px solid #3e9cff;
      border-radius: 5px 5px 5px 5px;
    }

    .page_border {
      padding: 0 22rpx;
      font-size: 22rpx;

      line-height: 44rpx;
      color: #777777;
      background: #f5f5f5;
      border-radius: 5px 5px 5px 5px;
    }
  }
}

.input_font {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #777777;
}

.page_box {
  box-sizing: border-box;
  width: 100%;
  padding: 50rpx;
  padding-top: 140rpx;
}

.page_flex_left_just {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.page_flex_row_bottom {
  width: 100%;
  padding-top: 30rpx;
  padding-bottom: 10rpx;
}

.tag-name {
  padding-bottom: 10rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
}

.tag-select-r-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag-select-r {
  width: 270rpx;
  padding: 15rpx 0rpx;
  margin: 20rpx 0rpx;
  font-size: 32rpx;
  font-size: 400;
  color: #000;
  text-align: center;

  border-radius: 10rpx;
}
.tag-select-r-normal {
  background-color: #f2f2f2;
}
.myStyle-box {
  color: #3e9cff;
  background-color: #f1f1ff;
  border: 1rpx solid #3e9cff;
}

.page_padding {
  padding: 50rpx 50rpx;
  background: #ffffff;
  border-radius: 20px 20px 20px 20px;
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
}

.page_input {
  padding-top: 16rpx;
  font-size: 26rpx;
  color: #666;
}

.page_input1 {
  padding-top: 10rpx;
}

.page_flex_left_row {
  display: flex;
  flex-direction: row;
  align-items: center;

  .page_flex_left_color {
    font-size: 11px;
    font-weight: 500;
    line-height: 22px;
    color: #333333;
  }

  .page_flex_des {
    font-size: 22rpx;
    font-weight: 500;
    line-height: 44rpx;
    color: #888888;
  }
}

.page_flex {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .page_flex_left {
    position: relative;
    padding-top: 20rpx;
    padding-bottom: 10rpx;
    font-size: 46rpx;
    font-weight: 600;
    color: #000000;
  }

  .page_flex_icon {
    position: absolute;
    right: 60rpx;

    .page_flex_img {
      z-index: 1001;
      width: 400rpx;
      height: 400rpx;
      background-repeat: no-repeat;
      background-position: 100% 100%;
      background-size: 100% 100%;
      @include graph-img('/static/img/Mask_group');
    }
  }

  // .page_flex_right{
  // 	font-weight: 600;
  // 	font-size: 18px;
  // 	color: #000000;
  // 	line-height: 22px;
  // }
}
</style>
