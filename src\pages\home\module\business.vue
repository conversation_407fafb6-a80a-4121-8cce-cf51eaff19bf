<template>
  <z-paging ref="pagingRef" auto v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          safe-area-inset-top
          placeholder
          custom-class="!bg-transparent px-30rpx !lh-normal"
        >
          <template #left>
            <text class="c-#333333 text-48rpx font-500">电商运营</text>
          </template>
        </wd-navbar>
        <view class="p-52rpx flex items-center justify-between">
          <view class="w-200rpx">
            <wd-tabs v-model="jobTabsStatus" color="#000000" inactive-color="#000000">
              <wd-tab
                v-for="item in jobStatusTabsList"
                :key="item"
                :title="`${item}`"
                :name="item"
              />
            </wd-tabs>
          </view>
          <view class="flex items-center gap-4rpx">
            <text class="c-#555555 text-24rpx">筛选</text>
            <text class="i-carbon-triangle-down-solid text-12rpx c-#333333" />
          </view>
        </view>
      </wd-config-provider>
    </template>
    <view class="p-[0_30rpx_44rpx] flex flex-col gap-40rpx">
      <personal-list v-for="(item, key) in pageData" :key="key" />
    </view>
    <template #bottom>
      <customTabbar name="home" />
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import customTabbar from '@/components/common/custom-tabbar.vue'
import personalList from '@/components/common/personal-list.vue'

defineOptions({
  name: 'HomeBusiness',
})
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const jobTabsStatus = ref('推荐')
const jobStatusTabsList = ['推荐', '最新']
const themeVars: ConfigProviderThemeVars = {
  navbarHeight: '120rpx',
  tabsNavLineBgColor: '#FF9191',
  tabsNavHeight: '48rpx',
  tabsNavLineHeight: '4rpx',
  tabsNavLineWidth: '58rpx',
}

function queryList() {
  pagingRef.value.completeByNoMore([1, 2, 3, 4, 5, 6, 7, 8, 9], true)
}
</script>

<style lang="scss" scoped>
:deep(.wd-navbar__left) {
  align-items: end;
}
:deep(.wd-tabs) {
  background: transparent;
  .wd-tabs__nav {
    background: transparent;
  }
  .wd-tabs__nav-item {
    &.is-active {
      font-size: 32rpx;
    }
  }
}
</style>
