<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="消息"></CustomNavBar>
    </template>
    <view class="contanner-page">
      <view class="contanner-item flex-between" @click="goSysPage">
        <view class="contanner-left flex-c flex-1">
          <view class="contanner-left-img info1">
            <wd-img :width="20" :height="20" :src="info1" />
          </view>
          <view class="m-l-10rpx message">
            <view class="flex-c">
              <view class="msg-text-1 m-r-20rpx">系统通知</view>
              <view class="msg-text-time">
                {{ infoList.xiTongNoticeVO?.createAdminTime.substring(0, 10) }}
              </view>
            </view>
            <view class="msg-text p-t-10rpx u-line-1 description">
              {{ infoList.xiTongNoticeVO?.title }}
            </view>
          </view>
        </view>
        <view class="contanner-right" v-if="infoList.xiTongNoticeVO?.unReadTotal">
          <view class="contanner-right-num">{{ infoList.xiTongNoticeVO?.unReadTotal }}</view>
        </view>
      </view>
      <view class="contanner-item flex-between" @click="goActive">
        <view class="contanner-left flex-c flex-1">
          <view class="contanner-left-img info4">
            <wd-img :width="20" :height="20" :src="info4" />
          </view>
          <view class="m-l-10rpx message">
            <view class="flex-c">
              <view class="msg-text-1 m-r-20rpx">活动通知</view>
              <view class="msg-text-time">
                {{ infoList.activitiNoticeVO?.createTime.substring(0, 10) }}
              </view>
            </view>
            <view class="msg-text p-t-10rpx u-line-1">
              {{ infoList.activitiNoticeVO?.title }}
            </view>
          </view>
        </view>
        <view class="contanner-right" v-if="infoList.activitiNoticeVO?.unReadTotal > 0">
          <view class="contanner-right-num">
            {{ infoList.activitiNoticeVO?.unReadTotal }}
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { noticeList } from '@/interPost/messege'
import info1 from '@/chartPage/img/info1.png'
import info2 from '@/chartPage/img/info2.png'
import info3 from '@/chartPage/img/info3.png'
import info4 from '@/chartPage/img/info4.png'
import joy from '@/static/img/1.jpg'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const infoList = ref({})
// 去系统消息
const goSysPage = () => {
  uni.navigateTo({
    url: '/chartPage/message/messageSysList?messageType=1',
  })
}
// 去活动消息
const goActive = () => {
  uni.navigateTo({
    url: '/chartPage/message/messageActiveList',
  })
}
// 获取消息
const getMessgeList = async () => {
  const res: any = await noticeList()

  if (res.code === 0) {
    infoList.value = res.data
  }
}

onLoad(async () => {
  await uni.$onLaunched
  getMessgeList()
})
</script>

<style scoped lang="scss">
.msg-text-1 {
  font-size: 26rpx;
  font-weight: 500;
  color: #333;
}
.msg-text-time {
  font-size: 26rpx;
  color: #888;
}
.msg-text {
  font-size: 24rpx;
  color: rgba(85, 85, 85, 1);
}
.info1 {
  background: rgba(255, 142, 142, 1);
}
.info2 {
  background: rgba(64, 166, 255, 1);
}
.info3 {
  background: rgba(255, 115, 50, 1);
}
.info4 {
  background: rgba(255, 193, 142, 1);
}
.contanner-page {
  padding: 0rpx 40rpx 40rpx;
  .contanner-item {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid rgba(218, 218, 218, 1);
    .contanner-left {
      width: calc(100% - 100rpx);
      margin-right: 20rpx;
      .contanner-left-img {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 90rpx;
        height: 90rpx;
        margin-right: 10rpx;
        border-radius: 50%;
      }
      .message {
        width: calc(100% - 110rpx);
      }

      .description {
        width: calc(100% - 20rpx);
      }
    }
  }
  .contanner-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40rpx;
    height: 40rpx;
    background: rgba(255, 71, 71, 1);
    border-radius: 50%;
    .contanner-right-num {
      font-size: 18rpx;

      color: #fff;
    }
  }
}
</style>
