<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="所属部门">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
        <template #right>
          <view @click="submit">确认</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="corporateName">
      <wd-input no-border v-model="department" placeholder="请输入部门名称" focus />
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { useResumeStore } from '@/store'
// 公司
const resumeStore = useResumeStore()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const department = ref('')
// 初始化
const initNane = ref('')
onLoad(async (options) => {
  await nextTick()
  department.value = options.department
  initNane.value = options.department
})
const submit = () => {
  resumeStore.setDepartment(department.value)
  uni.navigateBack()
}
// 返回
const back = () => {
  if (department.value !== initNane.value) {
    uni.showModal({
      title: '提示',
      content: '您有内容未提交保存,确认返回吗?',
      // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
      success: function (res) {
        if (res.confirm) {
          resumeStore.setCompany(initNane.value)
          uni.navigateBack()
          // console.log('用户点击了确定');
        }
      },
    })
  } else {
    uni.navigateBack()
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
::v-deep .wd-input__placeholder {
  font-size: 32rpx !important;
}
::v-deep .wd-input__inner {
  font-size: 32rpx !important;
  font-weight: 500;
}
.corporateName {
  padding-bottom: 40rpx;
  margin: 40rpx 40rpx;
  border-bottom: 1rpx solid #c0bfbf;
}
</style>
