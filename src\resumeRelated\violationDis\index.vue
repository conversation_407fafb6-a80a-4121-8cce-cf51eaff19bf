<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" v-model="pageData" @query="queryList" ref="pagingRef">
    <template #top>
      <CustomNavBar title="违规公示"></CustomNavBar>
    </template>
    <view class="qualiCertificate-list">
      <view
        class="qualiCertificate-item flex-between m-b-20rpx"
        v-for="(item, index) in pageData"
        :key="index"
      >
        <view class="flex-c">
          <image :src="img" class="qualiCertificate-img m-r-20rpx"></image>
          <view class="u-line-2 text-w text-24rpx c-#333 font-400">
            {{ item.content }}
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import img from '@/static/mine/business/mine.png'
import { queryListwg } from '@/interPost/home'
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 参数
const params = ref({
  entity: {
    userId: null,
  },
  orderBy: {},
  page: pageInfo.pageNum,
  size: pageInfo.pageSize,
})
// 获取列表
const queryList = async () => {
  const res: any = await queryListwg(params.value)
  if (res.code === 0) {
    pagingRef.value.complete(res.data.list)
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.qualiCertificate {
  padding: 40rpx;
  .qualiCertificate-btn {
    width: 100%;
    height: 120rpx;
    font-weight: 500;
    line-height: 120rpx;
    color: #fff;
    text-align: center;
    background: rgba(83, 120, 255, 1);
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
  }
}
.qualiCertificate-list {
  padding: 40rpx 40rpx 40rpx;
  .qualiCertificate-item {
    width: 100%;
    // height: 120rpx;
    padding: 4% 40rpx;
    font-weight: 500;
    // line-height: 120rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
    .qualiCertificate-img {
      width: 50rpx;
      height: 55rpx;
    }
    .text-w {
      width: calc(100% - 75rpx);
    }
  }
}
</style>
