//@ts-nocheck
import emoji1 from "../assets/emojis/U+1F600.png";
import emoji2 from "../assets/emojis/U+1F604.png";
import emoji3 from "../assets/emojis/U+1F609.png";
import emoji4 from "../assets/emojis/U+1F62E.png";
import emoji5 from "../assets/emojis/U+1F92A.png";
import emoji6 from "../assets/emojis/U+1F60E.png";
import emoji7 from "../assets/emojis/U+1F971.png";
import emoji8 from "../assets/emojis/U+1F974.png";
import emoji9 from "../assets/emojis/U+263A.png";
import emoji10 from "../assets/emojis/U+1F641.png";
import emoji11 from "../assets/emojis/U+1F62D.png";
import emoji12 from "../assets/emojis/U+1F610.png";
import emoji13 from "../assets/emojis/U+1F607.png";
import emoji14 from "../assets/emojis/U+1F62C.png";
import emoji15 from "../assets/emojis/U+1F913.png";
import emoji16 from "../assets/emojis/U+1F633.png";
import emoji17 from "../assets/emojis/U+1F973.png";
import emoji18 from "../assets/emojis/U+1F620.png";
import emoji19 from "../assets/emojis/U+1F644.png";
import emoji20 from "../assets/emojis/U+1F910.png";
import emoji21 from "../assets/emojis/U+1F97A.png";
import emoji22 from "../assets/emojis/U+1F928.png";
import emoji23 from "../assets/emojis/U+1F62B.png";
import emoji24 from "../assets/emojis/U+1F637.png";
import emoji25 from "../assets/emojis/U+1F912.png";
import emoji26 from "../assets/emojis/U+1F631.png";
import emoji27 from "../assets/emojis/U+1F618.png";
import emoji28 from "../assets/emojis/U+1F60D.png";
import emoji29 from "../assets/emojis/U+1F922.png";
import emoji30 from "../assets/emojis/U+1F47F.png";
import emoji31 from "../assets/emojis/U+1F92C.png";
import emoji32 from "../assets/emojis/U+1F621.png";
import emoji33 from "../assets/emojis/U+1F44D.png";
import emoji34 from "../assets/emojis/U+1F44E.png";
import emoji35 from "../assets/emojis/U+1F44F.png";
import emoji36 from "../assets/emojis/U+1F64C.png";
import emoji37 from "../assets/emojis/U+1F91D.png";
import emoji38 from "../assets/emojis/U+1F64F.png";
import emoji39 from "../assets/emojis/U+2764.png";
import emoji40 from "../assets/emojis/U+1F494.png";
import emoji41 from "../assets/emojis/U+1F495.png";
import emoji42 from "../assets/emojis/U+1F4A9.png";
import emoji43 from "../assets/emojis/U+1F48B.png";
import emoji44 from "../assets/emojis/U+2600.png";
import emoji45 from "../assets/emojis/U+1F31C.png";
import emoji46 from "../assets/emojis/U+1F308.png";
import emoji47 from "../assets/emojis/U+2B50.png";
import emoji48 from "../assets/emojis/U+1F31F.png";
import emoji49 from "../assets/emojis/U+1F389.png";
import emoji50 from "../assets/emojis/U+1F490.png";
import emoji51 from "../assets/emojis/U+1F382.png";
import emoji52 from "../assets/emojis/U+1F381.png";

export const emoji = {
  map: {
    "😀": { alt: "[哈哈]", url: emoji1 },
    "😄": { alt: "[笑]", url: emoji2 },
    "😉": { alt: "[眨眼]", url: emoji3 },
    "😮": { alt: "[惊讶]", url: emoji4 },
    "🤪": { alt: "[搞怪]", url: emoji5 },
    "😎": { alt: "[酷]", url: emoji6 },
    "🥱": { alt: "[困]", url: emoji7 },
    "🥴": { alt: "[眩晕]", url: emoji8 },
    "☺️": { alt: "[微笑]", url: emoji9 },
    "🙁": { alt: "[难过]", url: emoji10 },
    "😭": { alt: "[大哭]", url: emoji11 },
    "😐": { alt: "[无表情]", url: emoji12 },
    "😇": { alt: "[天使]", url: emoji13 },
    "😬": { alt: "[尴尬]", url: emoji14 },
    "🤓": { alt: "[书呆子]", url: emoji15 },
    "😳": { alt: "[脸红]", url: emoji16 },
    "🥳": { alt: "[庆祝]", url: emoji17 },
    "😠": { alt: "[生气]", url: emoji18 },
    "🙄": { alt: "[翻白眼]", url: emoji19 },
    "🤐": { alt: "[闭嘴]", url: emoji20 },
    "🥺": { alt: "[哀求]", url: emoji21 },
    "🤨": { alt: "[挑眉]", url: emoji22 },
    "😫": { alt: "[疲惫]", url: emoji23 },
    "😷": { alt: "[生病]", url: emoji24 },
    "🤒": { alt: "[感冒]", url: emoji25 },
    "😱": { alt: "[尖叫]", url: emoji26 },
    "😘": { alt: "[飞吻]", url: emoji27 },
    "😍": { alt: "[爱慕]", url: emoji28 },
    "🤢": { alt: "[恶心]", url: emoji29 },
    "👿": { alt: "[恶魔]", url: emoji30 },
    "🤬": { alt: "[爆粗]", url: emoji31 },
    "😡": { alt: "[愤怒]", url: emoji32 },
    "👍": { alt: "[赞]", url: emoji33 },
    "👎": { alt: "[踩]", url: emoji34 },
    "👏": { alt: "[鼓掌]", url: emoji35 },
    "🙌": { alt: "[庆祝]", url: emoji36 },
    "🤝": { alt: "[握手]", url: emoji37 },
    "🙏": { alt: "[祈祷]", url: emoji38 },
    "❤️": { alt: "[心]", url: emoji39 },
    "💔": { alt: "[心碎]", url: emoji40 },
    "💕": { alt: "[双心]", url: emoji41 },
    "💩": { alt: "[便便]", url: emoji42 },
    "💋": { alt: "[嘴唇]", url: emoji43 },
    "☀️": { alt: "[太阳]", url: emoji44 },
    "🌜": { alt: "[月亮]", url: emoji45 },
    "🌈": { alt: "[彩虹]", url: emoji46 },
    "⭐": { alt: "[星]", url: emoji47 },
    "🌟": { alt: "[亮星]", url: emoji48 },
    "🎉": { alt: "[派对]", url: emoji49 },
    "💐": { alt: "[花束]", url: emoji50 },
    "🎂": { alt: "[蛋糕]", url: emoji51 },
    "🎁": { alt: "[礼物]", url: emoji52 }
  }
};

const emojiList = Object.keys(emoji.map).map((key) => {
  return {
    //@ts-ignore
    alt: emoji.map[key].alt,
    //@ts-ignore
    url: emoji.map[key].url
  };
});

const emojiAltMap = Object.keys(emoji.map).reduce((acc, key) => {
  //@ts-ignore
  acc[emoji.map[key].alt] = key;
  return acc;
}, {});

export { emojiList, emojiAltMap, emoji1 };