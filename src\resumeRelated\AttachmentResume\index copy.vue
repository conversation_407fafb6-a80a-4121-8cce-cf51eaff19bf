<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="附件简历"></CustomNavBar>
    </template>
    <view class="box">
      <view class="box-list">
        <view
          class="box-list-item flex-between m-b-30rpx"
          v-for="(item, index) in fileList"
          :key="index"
        >
          <view class="flex-c" @click="previewPdf(item)">
            <wd-img :width="38" :height="38" :src="pdf" />
            <view class="m-l-30rpx">
              <view class="text-28rpx c-#333333 p-b-10rpx">{{ item.fileName }}</view>
              <view class="text-24rpx c-#888888">{{ item.createTime }}</view>
            </view>
          </view>
          <wd-img :width="20" :height="20" :src="del" @click.stop="delpdf(item.id)"></wd-img>
        </view>
        <view class="box-list-item flex-between m-b-30rpx">
          <view class="text-28rpx c-#333333">上传新附件({{ fileList.length }}/3)</view>
          <wd-img
            :width="20"
            :height="20"
            :src="addpdf"
            @click="addPdfImg3"
            v-if="fileList.length >= 3"
          ></wd-img>
          <wd-img :width="20" :height="20" :src="addpdf" @click="addPdfImg" v-else></wd-img>
        </view>
      </view>
    </view>

    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box">
          <view class="btn_bg" @click="GenerateResume">生成简历附件</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import CommonLink from '@/components/CommonLink/CommonLinkMy.vue'
import pdf from '@/resumeRelated/img/pdfimg.png'
import del from '@/resumeRelated/img/del.png'
import addpdf from '@/resumeRelated/img/addpdf.png'
import { queryMyFileResumeList, deleteFileResume, addFileResume } from '@/interPost/resume'
const { getToken } = useUserInfo()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const fileId = ref(null)
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
// 生成简历
const GenerateResume = () => {
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/GeneratePDF`,
  })
}
// 图片上传路径
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'
onShow(() => {
  getList()
})
const fileList = ref([])
// 获取列表
const getList = async () => {
  const res: any = await queryMyFileResumeList()
  if (res.code === 0) {
    fileList.value = res.data
  }
}
// 查看pdf
const previewPdf = (item: any) => {
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/WebViewpdf?fileName=${item.fileName}&fileUrl=${item.fileUrl}`,
  })
}
// 删除
const delpdf = async (id: any) => {
  uni.showModal({
    title: '提示',
    content: '您确定要删除吗?',
    // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
    success: function (res) {
      if (res.confirm) {
        deleteFileResume({ id }).then((res: any) => {
          if (res.code === 0) {
            getList()
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none',
              duration: 3000,
            })
          }
        })
      }
    },
  })
}
// 开始上传
const addPdfImg = async () => {
  uni.chooseFile({
    count: 1, // 选择单个文件
    type: 'file', // 通用文件类型（H5需单独限制accept）
    extension: ['.pdf'], // 小程序/App 限制扩展名
    success: (res) => {
      const file = res.tempFiles[0] // 获取文件对象
      uploadFile(file.path) // 调用上传方法
    },
    fail: (err) => {
      console.error('选择文件失败:', err)
    },
  })
}
// 上传成功后
const uploadFile = (filePath) => {
  uni.uploadFile({
    url: baseUrl, // 替换为你的上传接口
    filePath, // 本地文件路径
    name: 'file', // 后端接收的字段名
    header: {
      token: getToken(), // 从本地缓存获取token
    },
    success: (res: any) => {
      const resData: any = JSON.parse(res.data)
      if (resData.code === 0) {
        addFilePDF(resData.data[0].fileId)
      }
    },
  })
}
// 上传
const addFilePDF = async (fileId) => {
  const res: any = await addFileResume({ fileId })
  if (res.code === 0) {
    getList()
    uni.showToast({ title: '上传成功', icon: 'none' })
  } else {
    uni.showToast({ title: '上传失败', icon: 'none' })
  }
}
const addPdfImg3 = () => {
  uni.showToast({ title: '最多上传3个附件', icon: 'none' })
}
</script>

<style lang="scss" scoped>
.box {
  padding: 40rpx 60rpx;
  .box-list {
    .box-list-item {
      padding: 30rpx 40rpx;
      background: #fff;
      border-radius: 30rpx;
      box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
    }
  }
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
