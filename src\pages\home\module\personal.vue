<template>
  <z-paging ref="pagingRef" v-model="pageData" @query="queryList" :paging-style="pageStyle">
    <template #top>
      <CustomNavBar :fixed="false">
        <template #left>
          <view class="title">{{ title }}</view>
        </template>
        <template #right>
          <view class="content_list_adress" @click="searchPosition">
            <view class="text-28rpx">{{ cityName }}</view>
            <wd-icon name="caret-down-small" size="20px" color="#999"></wd-icon>
          </view>
        </template>
        <template #content>
          <view class="page-top">
            <view class="content_flex">
              <view class="content_search">
                <view class="content_search_bg">
                  <view class="content_search_left">
                    <image
                      class="_image"
                      src="/static/images/home/<USER>"
                      mode="aspectFill"
                    ></image>
                  </view>
                  <view class="content_search_right">
                    <wd-input
                      type="text"
                      no-border
                      placeholder="搜索您想要的内容"
                      v-model="params.entity.keyword"
                      confirm-type="search"
                      @confirm="confirm"
                    ></wd-input>
                  </view>
                </view>
              </view>
            </view>
            <view class="content_search content_search-p">
              <view class="content_list">
                <view class="content_list_left">
                  <view class="content_list_left_bg">
                    <scroll-view :scroll-x="true" class="scroll-view">
                      <view
                        class="content_list_for"
                        v-for="(item, index) in tagList"
                        :key="index"
                        @click="handSelect(index, item)"
                      >
                        <view :class="index == 0 ? 'content_list_border' : 'content_list_border_1'">
                          <view :class="selectIndex == index ? 'select_border' : 'select_noBorder'">
                            {{ item.expectedPositions }}
                          </view>
                        </view>
                      </view>
                    </scroll-view>
                    <image
                      class="_img"
                      src="/static/img/add.png"
                      mode="aspectFill"
                      @click="goJobExpectations"
                    ></image>
                  </view>
                </view>

                <view class="content_list_right-1" @click="handleSentResumes">
                  <image class="_img" src="/static/img/tdJob.png" mode="aspectFill"></image>

                  <!-- <view class="content_list_icon">
                    <image
                      class="_img"
                      src="/static/images/home/<USER>"
                      mode="aspectFill"
                    ></image>
                  </view>
                  <view
                    class="content_list_adress"
                    style="padding-left: 20rpx"
                    @click="searchPosition"
                  >
                    <view>重庆市</view>
                    <wd-icon name="caret-down-small" size="20px" color="#999"></wd-icon>
                  </view> -->
                </view>
              </view>
            </view>
            <view class="content_search_list content_search-p-t">
              <view class="content_search_list_flex">
                <view
                  class="content_list_left content_list_left-w"
                  style="display: flex; flex-direction: row"
                >
                  <view
                    class="content_list_left_for"
                    v-for="(item, index) in typeList"
                    :key="index"
                    @click="handType(index)"
                  >
                    <view
                      class=""
                      style="display: flex; flex-direction: column; align-items: center"
                    >
                      <view
                        class="text-28rpx c-#333333"
                        :class="
                          selectTag === index
                            ? 'content_list_left_color'
                            : 'content_list_left_color1'
                        "
                      >
                        {{ item.name }}
                      </view>
                      <view class="content_list_left_xian" v-if="selectTag === index"></view>
                    </view>
                  </view>
                </view>
                <view class="content_list_right flex-c">
                  <!-- <wd-icon name="add" size="12px" color="#333" class="p-r-20rpx"></wd-icon> -->
                  <view class="content_list_adress" @click="goFilter">
                    <view style="white-space: nowrap" class="text-26rpx c-#333">筛选</view>
                    <wd-icon name="caret-down-small" size="20px" color="#999"></wd-icon>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
      </CustomNavBar>
    </template>
    <view class="bg_flex-1" v-if="selectTag === 1">
      <view
        class="bg_box"
        v-for="(item, index) in positionList"
        :key="index"
        :class="activeChange === index ? 'activePositin' : 'nomalPositin'"
        @click="changePosition(index)"
      >
        {{ item.name }}
      </view>
    </view>
    <view class="page_list">
      <view class="page_flex" v-for="(item, index) in pageData" :key="index">
        <view class="page_flex_colom" @click="goDetail(item.id, item.companyId)">
          <view class="page_flex_list">
            <view class="flex-c">
              <view class="job-tag" v-if="item.isRecruit === 1">极招</view>
              <view class="page_left">
                {{ item.positionName }}
              </view>
              <view class="stateType" v-if="item.jobType === 2 || item.jobType === 3">
                {{ item.jobType === 2 ? '兼职' : item.jobType === 3 ? '实习' : '' }}
              </view>
            </view>

            <view class="page_right salary">
              {{ item.workSalaryBegin }}-{{ item.workSalaryEnd }}
            </view>
          </view>
          <view class="page_flex_list">
            <view class="page_left_1">{{ item.name }}·{{ item.sizeName }}</view>
            <view class="page_right_flex">
              <wd-icon name="location" size="14px" color="#999"></wd-icon>
              <view class="page_right_distance">
                {{ item.distanceMeters ? item.distanceMeters : item.districtName }}
              </view>
            </view>
          </view>
          <view class="bg_flex">
            <view class="bg_box" v-for="(subName, index) in item.positionKey" :key="index">
              {{ subName }}
            </view>
          </view>
          <view class="bg_end">
            <view class="bg_left">
              <image
                v-if="item.sex === 1"
                class="bg_left_icon"
                :src="item.hrPositionUrl ? item.hrPositionUrl : '/static/header/jobhunting1.png'"
                mode="aspectFill"
              ></image>
              <image
                v-else
                class="bg_left_icon"
                :src="item.hrPositionUrl ? item.hrPositionUrl : '/static/header/jobhunting2.png'"
                mode="aspectFill"
              ></image>
              <view class="bg_left_flex">
                <view class="bg_left_name">
                  {{ item.hrPositionName }}
                  <text v-if="item.hrPosition">·</text>
                  {{ item.hrPosition }}
                </view>
                <view class="bg_left_date">{{ item.status }}</view>
              </view>
            </view>
            <view class="flex-c">
              <view class="bg_right m-r-20rpx" @click.stop="goJob(item)">
                <image
                  class="bg_right_icon-1"
                  src="/static/img/td-job-card.png"
                  mode="aspectFill"
                ></image>
              </view>
              <view class="bg_right" @click.stop="goChat(item)">
                <image
                  class="bg_right_icon"
                  src="/static/images/home/<USER>"
                  mode="aspectFill"
                ></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <template #bottom>
      <customTabbar name="home" />
    </template>
  </z-paging>
  <resumes-sent
    v-model:show="sentResumesBool"
    :params="{
      cityCode: params.entity.cityCode,
      districtCode: params.entity.districtCode,
      positionCode: params.entity.expectedPositions,
      provinceCode: params.entity.provinceCode,
    }"
  />
  <wd-toast />
  <wd-message-box />
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBarBig.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import { getqueryList, getmyAddress, positionInfoByjob } from '@/interPost/home'
import { numberTokw } from '@/utils/common'
import { useLoginStore, useResumeStore } from '@/store'
import resumesSent from '@/components/common/resumes-sent.vue'

defineOptions({
  name: 'HomePersonal',
})
const message = useMessage()
const { getCurrentLocation } = useLocationPermission()
const { userRoleIsRealName } = useUserInfo()
const { sendGreetingMessage, sendResumeMessage } = useIMConversation()
const { bool: sentResumesBool, setTrue: sentResumesBoolTrue } = useBoolean()
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const resumeStore = useResumeStore()
// vuex数据
const loginStore = useLoginStore()
const title = ref('')
const activeChange = ref(0)
const cityName = ref('')
const homeCity = ref({})
const params = reactive({
  orderBy: {},
  entity: {
    // 省
    provinceName: '',
    provinceCode: '',
    // 市
    cityName: '',
    cityCode: '',
    // 区
    districtName: '',
    districtCode: '',
    // 关键字
    keyword: '',
    baseInfoId: null,
    expectedCity: '',
    expectedCityCode: '',
    expectedIndustry: '',
    expectedIndustryCode: '',
    expectedPositions: '',
    expectedPositionsCode: '',
    jobType: null,
    salaryExpectationEnd: null,
    salaryExpectationStart: null,
    workEducational: null,
    isNews: null,
    isRecruit: null,
    distanceMeters: null,
    lon: null,
    lat: null, // 尾度
    workSalaryBegin: '',
    workSalaryEnd: '',
    sizeName: '',
    workExperienceStart: '',
    workExperienceEnd: '',
  },
  size: pageInfo.pageSize,
  page: pageInfo.pageNum,
})
const selectTag = ref(0)
const selectIndex = ref(0)
const typeList = ref([
  {
    name: '推荐',
    value: '1',
  },
  {
    name: '附近',
    value: '2',
  },
  {
    name: '最新',
    value: '3',
  },
  {
    name: '极招',
    value: '4',
  },
])
// 求职期望
const goJobExpectations = () => {
  uni.navigateTo({
    url: '/resumeRelated/jobExpectations/index?isShow=' + 'home',
  })
}
const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}
// 去沟通item
const goChat = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      sendGreetingMessage(hxUserInfoVO.username, item)
    }
  } catch (error) {}
}
const tagList = ref([])
async function handleSentResumes() {
  try {
    await userToRealName()
    sentResumesBoolTrue()
  } catch (error) {}
}
const goJob = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      const num = await sendResumeMessage(hxUserInfoVO.username, item)
      if (!num) {
        message
          .confirm({
            title: '提示',
            msg: '请完善简历后再投递',
          })
          .then(() => {
            uni.navigateTo({
              url: '/resumeRelated/AttachmentResume/index',
            })
          })
          .catch()
      }
    }
  } catch (error) {}
}
const goFilter = () => {
  uni.navigateTo({
    url: '/resumeRelated/filter/index',
  })
}
const goDetail = (id: any, companyId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId}`,
  })
}
// 地址搜索
const searchPosition = () => {
  uni.navigateTo({
    url: '/resumeRelated/HomeRegion/index',
  })
}
// 附近
const positionList = ref([
  {
    name: '不限',
  },
  {
    name: '5km',
  },
  {
    name: '10km',
  },
  {
    name: '20km',
  },
  {
    name: '30km',
  },
])
// 获取3个职位
const jobList = async () => {
  const res: any = await positionInfoByjob()
  console.log('获取3个职位===')
  if (res.code === 0) {
    tagList.value = res.data
    params.entity.expectedPositions = tagList.value[0]?.expectedPositions
    title.value = tagList.value[0].expectedPositions
    await getRegenList()
  }
}
// 附近距离切换
const changePosition = (index) => {
  activeChange.value = index
  params.entity.distanceMeters = null
  if (index === 0) {
    params.entity.distanceMeters = null
  }
  if (index === 1) {
    params.entity.distanceMeters = 5 * 1000
  }
  if (index === 2) {
    params.entity.distanceMeters = 10 * 1000
  }
  if (index === 3) {
    params.entity.distanceMeters = 20 * 1000
  }
  if (index === 4) {
    params.entity.distanceMeters = 30 * 1000
  }
  pagingRef.value.reload()
}
// 切换列表
const handType = (index: any) => {
  selectTag.value = index
  params.entity.isNews = null
  params.entity.distanceMeters = null
  params.entity.isRecruit = null
  if (selectTag.value === 1) {
    getmyAddressList()
  }
  if (selectTag.value === 2) {
    params.entity.isNews = 1
  }
  if (selectTag.value === 3) {
    params.entity.isRecruit = 1
  }
  pagingRef.value.reload()
}
// 搜索
const confirm = () => {
  pagingRef.value.reload()
}
// 获取地址
const getmyAddressList = async () => {
  const res: any = await getmyAddress()
  if (res.code === 0) {
    if (res.data?.lat) {
      params.entity.lat = res.data.lat
      params.entity.lon = res.data.lon
    } else {
      uni.navigateTo({
        url: '/setting/AdressMange/index',
      })
    }
  }
}
// 获取列表positionKey
const queryList = async () => {
  console.log('获取列表position')
  params.entity.expectedPositions = tagList.value[selectIndex.value].expectedPositions
  title.value = tagList.value[selectIndex.value].expectedPositions
  const res: any = await getqueryList(params)
  if (res.code === 0) {
    res.data?.list &&
      res.data.list.forEach((ele: any) => {
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryBegin =
          ele.workSalaryBegin === 0 ? '面议' : numberTokw(ele.workSalaryBegin + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '面议' : numberTokw(ele.workSalaryEnd + '')
        ele.distanceMeters = ele.distanceMeters
          ? Math.floor(parseInt(ele.distanceMeters) / 1000) + 'km'
          : ''
      })

    pagingRef.value.complete(res.data.list)
  }
}
// 获取查询地址和筛选条件
const getRegenList = () => {
  // 设置查询条件
  console.log(resumeStore.fillterObg, ' resumeStore.fillterObg===')
  const filter = resumeStore?.fillterObg
  if (filter) {
    params.entity.workSalaryBegin = filter.workSalaryBegin
    params.entity.workSalaryEnd = filter.workSalaryEnd
    params.entity.sizeName = filter.sizeName
    params.entity.workEducational = filter.workEducational
    params.entity.workExperienceStart = filter.workExperienceStart
    params.entity.workExperienceEnd = filter.workExperienceEnd
  }

  // 获取城市选中

  const activeIndex = loginStore.homeJobAvtive
  console.log(activeIndex, 'activeIndex==')
  const cityKey = `homeCity${activeIndex + 1}`
  const setCityFn = `sethomeCity${activeIndex + 1}`
  const defaultCity = tagList.value[activeIndex]
  console.log(tagList.value[activeIndex], 'tagList.value[activeIndex]')

  // 获取或设置城市信息

  const cityData = loginStore[cityKey]?.provinceName ? loginStore[cityKey] : defaultCity
  console.log(cityData, 'cityData=============111111')
  console.log(loginStore[cityKey], cityKey, 'cityData=============222')
  if (loginStore[cityKey].provinceName === '') {
    loginStore[setCityFn]({
      provinceName: defaultCity.provinceName,
      provinceCode: Number(defaultCity.provinceCode),
      cityCode: Number(defaultCity.cityCode),
      cityName: defaultCity.cityName,
      districtCode: defaultCity.provinceCode ? Number(defaultCity.districtCode) : '',
      districtName: defaultCity.districtName || '',
    })
  }

  // 设置参数
  params.entity.provinceName = cityData.provinceName
  params.entity.provinceCode = cityData.provinceCode
  params.entity.cityName = cityData.cityName
  params.entity.cityCode = cityData.cityCode
  params.entity.districtName = cityData.districtName || ''
  params.entity.districtCode = cityData.districtCode || ''
  // 设置显示的城市名
  cityName.value = cityData.districtName
    ? cityData.districtName
    : cityData.cityName || cityData.cityName

  pagingRef.value.reload()
}
const handSelect = (index: any, item: any) => {
  selectIndex.value = index
  loginStore.sethomeJobAvtive(index)
  title.value = item.expectedPositions
  params.entity.expectedPositions = null
  if (index === 0) {
    params.entity.expectedPositions = item.expectedPositions
  }
  if (index === 1) {
    params.entity.expectedPositions = item.expectedPositions
  }
  if (index === 2) {
    params.entity.expectedPositions = item.expectedPositions
  }
  getRegenList()
  pagingRef.value.reload()
}

onMounted(async () => {
  await uni.$onLaunched
  try {
    await getCurrentLocation()
  } catch (error) {}
  loginStore.sethomeJobAvtive(selectIndex.value)
  await nextTick()
  await jobList()
  uni.$on('refresh-a-page', getRegenList)
  uni.$on('refresh-a-jobList', jobList)
})

onUnmounted(() => {
  uni.$off('refresh-a-page', getRegenList)
  uni.$off('refresh-a-jobList', jobList)
})
</script>

<style lang="scss" scoped>
.salary {
  color: #ff8080 !important;
}
.content_list_left-w {
  width: calc(100% - 100rpx);
}
.content_search-p {
  padding: 30rpx 40rpx;
}
::v-deep .uni-scroll-view-content {
  text-align: left !important;
}
.stateType {
  padding: 0rpx 10rpx;
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #888888;
  text-align: center;
  border: 1rpx solid #888888;

  border-radius: 6rpx;
}
.sx {
  background: #adbaff;
}
.jz {
  background: #fda283;
}
.job-tag {
  padding: 0 20rpx;
  margin-right: 10rpx;
  font-size: 24rpx;
  color: #fff;
  background: #ff5151;
  border-radius: 20rpx 0rpx 20rpx 0rpx;
}
.content_search-p-t {
  padding: 0rpx 40rpx 0rpx;
}

.page_left_1 {
  font-size: 24rpx !important;
  font-weight: 400;
  line-height: 44rpx;
  color: #666;
}

.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 10rpx;
  .bg_right-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 132rpx;
    height: 64rpx;
    text-align: center;
    background: #f0f3fd;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    &_icon {
      width: 32rpx;
      height: 32rpx;
    }
  }
  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 132rpx;
    height: 64rpx;
    text-align: center;
    background: #fff4f4;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    &_icon {
      width: 40rpx;
      height: 40rpx;
    }
    &_icon-1 {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 24rpx;
        font-weight: 400;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 22rpx;
        font-weight: 400;
        color: #666;
        // line-height: 44rpx;
      }
    }
  }
}

.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  padding-top: 0rpx !important;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 0rpx 10rpx;
    // margin: 14rpx 0;
    margin-top: 14rpx;
    margin-right: 24rpx;
    font-size: 22rpx;
    font-weight: 400;
    line-height: 44rpx;
    color: #888888;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}
.bg_flex-1 {
  z-index: 1000;
  display: flex;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 20rpx;
  margin-top: 0rpx;
  .activePositin {
    color: #4d8fff;
    border: 1rpx solid #4d8fff;
  }
  .nomalPositin {
    color: #000;
  }
  .bg_box {
    padding: 0rpx 20rpx;
    // margin: 14rpx 0;
    margin-right: 22rpx;
    font-size: 24rpx;
    font-weight: 400;
    line-height: 44rpx;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.page_list {
  box-sizing: border-box;
  width: 100%;
  padding: 0 40rpx;
  // margin-bottom: 200rpx;

  .page_flex {
    width: 100%;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .page_flex_colom {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;

      .page_flex_list {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .page_right_flex {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 5rpx 0;

          .page_left_1 {
            font-size: 24rpx !important;
            font-weight: 400;
            line-height: 44rpx;
            color: #666;
          }

          .page_right_distance {
            font-size: 22rpx;
            font-weight: 400;
            line-height: 44rpx;
            color: #888888;
          }
        }

        .page_left {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #333333;
        }

        .page_right {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #888888;
        }
      }
    }
  }
}
.content_list_adress {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 24rpx;
  font-weight: 400;
  color: #555555;
}

.content_search_list_flex {
  display: flex !important;
  flex-direction: row;
  align-items: center;
  justify-content: space-between !important;
  padding-bottom: 20rpx;
}

.navigation-bar-betwween {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40rpx;
}

.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 50rpx;
  font-weight: 500;
  color: #000000;
}
.content_list_right-1 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 84rpx;
  height: 84rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
  ._img {
    width: 44rpx;
    height: 44rpx;
  }
}
.content_list_right {
  display: flex;
  flex-direction: row;
  align-items: center;

  .content_list_icon {
    display: flex;
    align-items: center;
    padding: 16rpx 10rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    ._img {
      width: 50rpx;
      height: 50rpx;
    }
  }
}

.content_list_left_for {
  display: flex;
  flex-direction: row;
  // width: 100%;
  padding-right: 50rpx;
}

.content_list_left_color {
  margin-bottom: -10rpx;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 44rpx;
  color: #000000;
}

.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    position: relative;
    // width: 80%;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: calc(100% - 100rpx);
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    ._img {
      position: absolute;
      right: 10rpx;
      width: 30rpx;
      height: 30rpx;
    }
    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: left;
      width: 100%;
      padding: 10rpx 60rpx 10rpx 20rpx;
      white-space: nowrap;
      // background: red;
      .content_list_for {
        display: inline-block;
        // margin: auto;

        .content_list_border_1 {
          padding-right: 30rpx;
          padding-left: 30rpx;
        }
      }
    }
  }
}

.select_border {
  width: 100%;
  padding: 11rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #333333;
  text-align: center;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}

.select_noBorder {
  font-size: 24rpx;
  line-height: 44rpx;
  color: #3e3e56;
  text-align: center;
}

.content_list_left_xian {
  width: 58rpx;
  height: 8rpx;
  font-weight: bold;
  background: #ff9191;
  border-radius: 2rpx 2rpx 2rpx 2rpx;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 20rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;
        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.content {
  // height: 100%;
  // background-color: rgba(244, 244, 244, 1);
  width: 100%;
}

.start_ICON {
  ._icon {
    width: 56rpx;
    height: 56rpx;
  }
}
</style>
