import type { pagingDataInt } from '@/service/types'
interface usePagingInt {
  style?: Partial<CSSStyleDeclaration>
}
/** 分页长用hooks */
export const usePaging = <T = AnyObject>(paging?: usePagingInt) => {
  /** z-paging组件实例 */
  const pagingRef = ref<ZPagingInstance>()
  /** 分页信息 */
  const pageInfo = reactive<Pick<pagingDataInt, 'pageNum' | 'pageSize'>>({
    pageNum: 1,
    pageSize: uni.$zp.config['default-page-size'] as number,
  })
  /** 分页数据 */
  const pageData = ref<T[]>([]) as Ref<T[]>
  /** z-paging的style */
  const pageStyle = paging?.style || {}

  const pageSetInfo = (page: number, size: number) => {
    pageInfo.pageNum = page
    pageInfo.pageSize = size
  }
  return {
    pagingRef,
    pageInfo,
    pageSetInfo,
    pageData,
    pageStyle,
  }
}
