<template>
  <view class="content">
    <z-paging
      ref="pagingRef"
      v-model="dataList"
      use-chat-record-mode
      safe-area-inset-bottom
      bottom-bg-color="#f8f8f8"
      empty-view-text="有什么可以帮忙的？"
      @query="queryList"
      @keyboardHeightChange="keyboardHeightChange"
      @hidedKeyboard="hidedKeyboard"
      :paging-style="pageStyle"
      empty-view-img="/static/img/deepSeek.png"
    >
      <template #top>
        <CustomNavBar title="deepseek">
          <template #left>
            <view class=""></view>
          </template>
          <template #right>
            <image class="tabbarImg" src="/static/img/<EMAIL>"></image>
          </template>
        </CustomNavBar>
      </template>

      <!-- for循环渲染聊天记录列表 -->
      <view v-for="(item, index) in dataList" :key="index" style="position: relative">
        <view style="transform: scaleY(-1)">
          <chat-item :item="item"></chat-item>
        </view>
      </view>
      <!-- 底部聊天输入框 -->
      <template #bottom>
        <chat-input-bar :disabled="isAnswering" ref="inputBar" @send="doSend" />
        <customTabbar name="deepseek" />
      </template>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import chatInputBar from '@/components/chat-input-bar/chat-input-bar.vue'
import chatItem from '@/components/chat-item/chat-item.vue'
import { formatDateTime } from '@/utils/common'
import { historyList, chat } from '@/interPost/deepSeek'

defineOptions({
  name: 'DeepSeekPersonal',
})
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// 分页
const params = ref({
  entity: {},
  orderBy: {},
  page: pageInfo.pageNum,
  size: pageInfo.pageSize,
})
// 控制显示新对话还是历史对话
const isShow = ref(true)
// 定义响应式变量
const inputBar = ref(null)
const dataList = ref([])
const askMsg = ref('')
const isAnswering = ref(false)

// 获取历史记录
const queryList = async () => {
  await uni.$onLaunched

  const res: any = await historyList(params.value)
  console.log(res, '历史记录')
  pagingRef.value.complete(res.data.list)
}
onLoad(async () => {
  await nextTick()
  pagingRef.value.reload()
})
// 监听键盘高度改变
const keyboardHeightChange = (res) => {
  inputBar.value.updateKeyboardHeightChange(res)
}

// 用户尝试隐藏键盘
const hidedKeyboard = () => {
  inputBar.value.hidedKeyboard()
}

// 发送新消息
const doSend = (msg) => {
  if (isAnswering.value) {
    // 如果在回答中，不允许发送新的消息，避免数据错乱
    return
  }

  askMsg.value = msg
  pagingRef.value.addChatRecordData({
    content: msg,
    icon: '/static/img/1.jpg',
    createTime: formatDateTime(),
    isMe: true,
    type: 0,
  })
  // 在用户发送新消息之后，开始回复消息
  doAnswer()
}

// 回复消息
const doAnswer = async () => {
  isAnswering.value = true
  try {
    // 1. 先发送一条初始消息
    pagingRef.value.addChatRecordData({
      createTime: formatDateTime(),
      icon: '/static/img/deepseek2.png',
      content: '思考中...',
      isMe: false,
      type: 1,
    })

    // 2. 调用 API 获取回答
    const res = await chat({ message: askMsg.value })
    if (!res) throw new Error('Empty response from API')

    const cleanAnswer = removeDataPrefix(res).replace(/\n+/g, ' ')
    askMsg.value = ''

    // 3. 流式输出
    let currentAnswerStr = ''
    await streamTextAsync(cleanAnswer, (char) => {
      currentAnswerStr += char
      // 确保 dataList 不为空
      if (dataList.value.length > 0) {
        dataList.value[0].content = currentAnswerStr.trim()
      }
    })
  } catch (error) {
    console.error('Error in doAnswer:', error)
    // 更新错误消息
    if (dataList.value.length > 0) {
      dataList.value[0].content = '抱歉，回答时出现问题'
    }
  } finally {
    isAnswering.value = false
  }
}

// 移除所有 "data:" 前缀（适用于单行或多行情况）
const removeDataPrefix = (text) => {
  if (!text) return text
  // 如果是多行数据，逐行过滤
  if (text.includes('\n')) {
    return text
      .split('\n')
      .map((line) => (line.startsWith('data:') ? line.substring(5) : line))
      .join('\n')
  }
  // 如果是单行数据，直接过滤
  return text.startsWith('data:') ? text.substring(5) : ''
}

// 模拟流式返回，逐个字符输出
const streamTextAsync = async (text, callback, interval = 150) => {
  // 确保传入的 text 是字符串
  const cleanText = String(text)
  for (const char of cleanText) {
    callback(char)
    await new Promise((resolve) => setTimeout(resolve, interval))
  }
}
</script>

<style scoped lang="scss">
.tabbarImg {
  width: 50rpx;
  height: 45rpx;
}
.header {
  padding: 20rpx;
  font-size: 20rpx;
  color: white;
  background-color: red;
}
.popup {
  position: absolute;
  top: -20px;
  z-index: 1000;
  width: 400rpx;
  height: 200rpx;
  background-color: red;
}
.start_photo {
  box-sizing: 220rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 200rpx;
  transform: rotate(180deg);
}
.start_deep {
  width: 340rpx;
  height: 340rpx;
}
</style>
