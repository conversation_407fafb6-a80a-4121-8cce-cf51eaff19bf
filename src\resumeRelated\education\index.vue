<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="教育经历">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="mainText labelName">学校</view>
        <view class="flex-c">
          <view
            class="text-white-space"
            :class="fromData.school ? 'selelctColor' : 'nomalColor'"
            @click="goSchool"
          >
            {{ fromData.school ? fromData.school : '学校名称' }}
          </view>
          <wd-icon
            @click="goSchool"
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">学历</view>
        <wd-picker :columns="columnsList" v-model="fromData.qualification" />
        <!-- <view class="text-34rpx font-w-500 flex-1 text-r">研究生·全日制</view> -->
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">专业</view>
        <view class="flex-c">
          <wd-input no-border v-model="fromData.major" placeholder="专业名称" />
          <view class="m-l-40rpx"></view>
          <!-- <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon> -->
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="mainText labelName">时间段</view>
        <view class="flex-c">
          <view
            class="text-32rpx text-pre-wrap"
            :class="fromData.startTime ? 'c-#333333' : 'c-#888888'"
            @click="showDateRangePicker"
          >
            {{ fromData.startTime ? fromData.startTime + '至' + fromData.endTime : '时间段' }}
          </view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>

        <!-- <view class="mainText labelName" @click="showDateRangePicker">选择时间段</view> -->
        <!-- <wd-datetime-picker v-model="timeData" @confirm="handleConfirm" type="year" /> -->
      </view>
    </view>

    <template #bottom>
      <view class="btn-fixed flex-c">
        <view
          v-if="isAdd === 'edit'"
          class="btn-delet m-r-30rpx"
          @click="delSubmit"
          :class="isAdd === 'edit' ? 'w-30' : ''"
        >
          删除
        </view>
        <view class="btn_box" :class="isAdd === 'edit' ? 'w-70' : 'w-100'">
          <view class="btn_bg" @click="submit">完成</view>
        </view>
      </view>
    </template>
    <date-range-picker
      ref="dateRangePicker"
      v-model="dateRange"
      @confirm="onDateRangeConfirm"
      @cancel="onDateRangeCancel"
    />
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import DateRangePicker from '@/components/range-picker/range-picker.vue'
import { useResumeStore } from '@/store'
import {
  resumeEducationalAdd,
  resumeEducationalUpdate,
  resumeEducationalDel,
} from '@/interPost/resume'
import isEqual from 'lodash/isEqual'

// z-paging配置
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const isAdd = ref(null)
const resumeStore = useResumeStore()
const objItem = ref(null)
const timeData = ref([])
const baseInfoId = ref([])

const dateRangePicker = ref(null)
const dateRange = ref([])

const showDateRangePicker = () => {
  dateRangePicker.value.open()
}

const onDateRangeConfirm = (e) => {
  fromData.value.startTime = e.value[0]
  fromData.value.endTime = e.value[1]
}

const onDateRangeCancel = () => {
  uni.showToast({
    title: '已取消选择',
    icon: 'none',
  })
}
// 表单
const fromData = ref({
  endTime: '',
  major: '',
  qualification: '',
  school: '',
  startTime: '',
})
// 表单初始化
const fromDataInit = ref(JSON.parse(JSON.stringify(fromData.value)))
// 学历
const columnsList = ref([
  {
    value: 1,
    label: '高中及以下',
  },
  {
    value: 2,
    label: '专科',
  },
  {
    value: 3,
    label: '本科',
  },
  {
    value: 4,
    label: '研究生',
  },
  {
    value: 5,
    label: '博士生',
  },
])

// 确认
const submit = async () => {
  console.log('====================')
  if (!fromData.value.school) {
    uni.showToast({
      title: '请输入学校名称',
      icon: 'none',
    })
    return
  }
  if (!fromData.value.qualification) {
    uni.showToast({
      title: '请选择学历',
      icon: 'none',
    })
    return
  }
  if (!fromData.value.major) {
    uni.showToast({
      title: '请输入专业',
      icon: 'none',
    })
    return
  }
  if (!fromData.value.startTime) {
    uni.showToast({
      title: '请选择时间段',
      icon: 'none',
    })
    return
  }
  if (isAdd.value === 'add') {
    const res: any = await resumeEducationalAdd({ ...fromData.value, baseInfoId: baseInfoId.value })
    console.log(res, 'res')
    if (res.code === 0) {
      resumeStore.setSchool('')
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  } else {
    const res: any = await resumeEducationalUpdate(fromData.value)
    console.log(res, 'res')
    if (res.code === 0) {
      resumeStore.setSchool('')
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
// 去学校
const goSchool = () => {
  uni.navigateTo({
    url: '/resumeRelated/education/school?school=' + fromData.value.school,
  })
}
// 时间确定
// const handleConfirm = (e) => {
//   fromData.value.startTime = e.value[0]
//   fromData.value.endTime = e.value[1]
// }
onLoad((options) => {
  // 简历id
  baseInfoId.value = options.id
  isAdd.value = options.isAdd
  if (isAdd.value === 'edit') {
    objItem.value = JSON.parse(decodeURIComponent(options.item))
    fromData.value = JSON.parse(decodeURIComponent(options.item))
    timeData.value = [fromData.value.startTime, fromData.value.endTime]
  }
})
// 删除
const delSubmit = () => {
  uni.showModal({
    title: '提示',
    content: '您确定要删除该条信息吗?',
    success: function (res) {
      if (res.confirm) {
        resumeEducationalDel({ id: objItem.value.id, baseInfoId: baseInfoId.value }).then(
          (res: any) => {
            if (res.code === 0) {
              resumeStore.setSchool('')
              uni.navigateBack()
            } else {
              uni.showToast({
                title: res.msg,
                icon: 'none',
                duration: 3000,
              })
            }
          },
        )
      }
    },
  })
}
// 返回
const back = () => {
  if (isAdd.value === 'add') {
    if (isEqual(fromData.value, fromDataInit.value)) {
      resumeStore.setSchool('')
      uni.navigateBack()
    } else {
      uni.showModal({
        title: '提示',
        content: '您有内容未提交保存,确认返回吗?',
        // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
        success: function (res) {
          if (res.confirm) {
            resumeStore.setSchool('')
            uni.navigateBack()
          }
        },
      })
    }
  } else {
    console.log('000000')
    if (
      objItem.value.endTime === fromData.value.endTime &&
      objItem.value.major === fromData.value.major &&
      objItem.value.qualification === fromData.value.qualification &&
      objItem.value.school === fromData.value.school &&
      objItem.value.startTime === fromData.value.startTime
    ) {
      resumeStore.setSchool('')
      uni.navigateBack()
    } else {
      uni.showModal({
        title: '提示',
        content: '您有内容未提交保存,确认返回吗?',
        // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
        success: function (res) {
          if (res.confirm) {
            resumeStore.setSchool('')
            uni.navigateBack()
          }
        },
      })
    }
  }
}
onShow(() => {
  fromData.value.school = resumeStore.school ? resumeStore.school : fromData.value.school
})
</script>

<style lang="scss" scoped>
::v-deep .wd-picker__cell {
  padding-right: 0rpx;
  background: transparent;
}
::v-deep .wd-picker__region.is-active {
  background: transparent;
}

::v-deep .uni-input-input {
  font-size: 32rpx;
  color: #333333;
}
::v-deep .wd-picker__placeholder {
  font-size: 32rpx;
  color: #888888;
}
::v-deep .wd-picker__value {
  margin-right: 0rpx;
  font-size: 32rpx;
  // color: #333333;
}
::v-deep .wd-input__placeholder {
  font-size: 32rpx;
  color: #888888 !important;
}
::v-deep .wd-picker__value {
  // font-size: 32rpx;
  font-weight: 500;
}
::v-deep .wd-picker__arrow {
  color: #888888;
}
.selelctColor {
  color: #333333;
}

.nomalColor {
  color: #888888;
}
::v-deep .wd-input {
  text-align: right;
  background-color: transparent;
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn-delet {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 30%;
    padding: 20rpx 0rpx;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background: #959595;
    border-radius: 14px 14px 14px 14px;
  }
  .btn_box {
    box-sizing: border-box;
    // width: 70%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.labelName {
  width: 200rpx;
  text-align: left;
}

.pageContaner {
  padding: 20rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
</style>
