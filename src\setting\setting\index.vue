<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="设置"></CustomNavBar>
    <view class="setting">
      <view class="setting-list flex-between border-b" @click="goAccountNumber">
        <view class="text-32rpx font-w-500">账号与安全中心</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goNotice">
        <view class="text-32rpx font-w-500">通知与提醒</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between">
        <view class="text-32rpx font-w-500">隐私保护</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between" @click="goGeneralSetup">
        <view class="text-32rpx font-w-500">通用设置</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between border-b">
        <view class="text-32rpx font-w-500">面试设置</view>
        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
      <view class="setting-list flex-between">
        <view class="text-32rpx font-w-500">版本更新</view>
        <view class="subText flex-c">
          <view class="p-r-10rpxrpx">0.0.80</view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="setting-list flex-between" @click="chanangeIdentity">
        <view class="text-32rpx font-w-500">切换身份</view>
        <view class="subText flex-c">
          <view class="p-r-10rpxrpx">{{ userRoleIsBusiness ? '伯乐' : '黑马' }}</view>
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>

      <view class="btn-flexd">
        <view class="btn-flexd-red" @click="logoutBtm">退出登录</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { logout } from '@/interPost/login'
import { clearStorageSync } from '@/utils/storege'
import { useLoginStore } from '@/store'
const { userRoleIsBusiness } = useUserInfo()
// 退出登录
// vuex数据
const loginStore = useLoginStore()
const logoutBtm = async () => {
  const res: any = await logout()
  if (res.code === 0) {
    loginStore.sethomeJobAvtive(0)
    loginStore.sethomeCity1({})
    loginStore.sethomeCity2({})
    loginStore.sethomeCity3({})
    clearStorageSync()
    uni.reLaunch({
      url: '/pages/login/index',
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 切换身份
const chanangeIdentity = () => {
  uni.navigateTo({
    url: '/setting/IdentitySwitching/index',
  })
}
const goNotice = () => {
  uni.navigateTo({
    url: '/setting/notice/index',
  })
}
const goAccountNumber = () => {
  uni.navigateTo({
    url: '/setting/accountNumber/index',
  })
}
const goGeneralSetup = () => {
  uni.navigateTo({
    url: '/setting/generalSetup/index',
  })
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}
.btn-flexd-red {
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  text-align: center;
  background-color: #ff8080;
  border-radius: 30rpx;
}

.btn-flexd {
  position: fixed;
  bottom: 80rpx;
  left: 10%;
  width: 80%;
  margin: auto;
}

::v-deep .u-button {
  height: 80rpx;
  border-radius: 30rpx;
}

::v-deep .u-button__text {
  font-size: 28rpx !important;
  font-weight: bold;
  color: #fff !important;
}
</style>
