<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" v-model="pageData" @query="queryList" :paging-style="pageStyle">
    <template #top>
      <CustomNavBar :fixed="false">
        <template #left>
          <wd-icon
            name="arrow-left"
            class="back-button"
            color="#000000"
            size="20"
            @click="goBack"
          />
        </template>
        <template #content>
          <view class="page-top">
            <view class="content_flex">
              <view class="content_search">
                <view class="content_search_bg">
                  <view class="content_search_left">
                    <image
                      class="_image"
                      src="/static/images/home/<USER>"
                      mode="aspectFill"
                    ></image>
                  </view>
                  <view class="content_search_right">
                    <wd-input
                      type="text"
                      no-border
                      placeholder="搜索您想要的内容"
                      v-model="params.entity.positionName"
                      confirm-type="search"
                      @confirm="confirm"
                    ></wd-input>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </template>
      </CustomNavBar>
    </template>
    <view class="page_list">
      <view class="page_flex" v-for="(item, index) in pageData" :key="index">
        <view class="page_flex_colom" @click="goDetail(item.id, item.companyId)">
          <view class="page_flex_list">
            <view class="flex-c">
              <view class="job-tag" v-if="item.isRecruit === 1">极招</view>
              <view class="page_left">
                {{ item.positionName }}
              </view>
              <view class="stateType" v-if="item.jobType === 2 || item.jobType === 3">
                {{ item.jobType === 2 ? '兼职' : item.jobType === 3 ? '实习' : '' }}
              </view>
            </view>

            <view class="page_right salary">
              {{ item.workSalaryBegin }}-{{ item.workSalaryEnd }}
            </view>
          </view>
          <view class="page_flex_list">
            <view class="page_left_1">{{ item.name }}·{{ item.sizeName }}</view>
            <view class="page_right_flex">
              <wd-icon name="location" size="14px" color="#999"></wd-icon>
              <view class="page_right_distance">
                {{ item.distanceMeters ? item.distanceMeters : item.districtName }}
              </view>
            </view>
          </view>
          <view class="bg_flex">
            <view class="bg_box" v-for="(subName, index) in item.positionKey" :key="index">
              {{ subName }}
            </view>
          </view>
          <view class="bg_end">
            <view class="bg_left">
              <image
                v-if="item.sex === 1"
                class="bg_left_icon"
                :src="item.hrPositionUrl ? item.hrPositionUrl : '/static/header/jobhunting1.png'"
                mode="aspectFill"
              ></image>
              <image
                v-else
                class="bg_left_icon"
                :src="item.hrPositionUrl ? item.hrPositionUrl : '/static/header/jobhunting2.png'"
                mode="aspectFill"
              ></image>
              <view class="bg_left_flex">
                <view class="bg_left_name">
                  {{ item.hrPositionName }}
                  <text v-if="item.hrPosition">·</text>
                  {{ item.hrPosition }}
                </view>
                <view class="bg_left_date">{{ item.activityStatus }}</view>
              </view>
            </view>
            <view class="flex-c">
              <view class="bg_right" @click.stop="goChat(item)">
                <image
                  class="bg_right_icon"
                  src="/static/images/home/<USER>"
                  mode="aspectFill"
                ></image>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
  <resumes-sent
    v-model:show="sentResumesBool"
    :params="{
      cityCode: params.entity.cityCode,
      districtCode: params.entity.districtCode,
      positionCode: params.entity.expectedPositions,
      provinceCode: params.entity.provinceCode,
    }"
  />
  <wd-toast />
  <wd-message-box />
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBarBig.vue'

import { queryPositionByCompanyId } from '@/interPost/home'
import { numberTokw } from '@/utils/common'
import resumesSent from '@/components/common/resumes-sent.vue'

defineOptions({
  name: 'HomePersonal',
})
const message = useMessage()
const { userRoleIsRealName } = useUserInfo()
const { sendGreetingMessage } = useIMConversation()
const { bool: sentResumesBool } = useBoolean()
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const title = ref('')

const cityName = ref('')
const params = reactive({
  orderBy: {},
  entity: {
    // 省
    provinceName: '',
    provinceCode: '',
    // 市
    cityName: '',
    cityCode: '',
    // 区
    districtName: '',
    districtCode: '',
    // 关键字
    keyword: '',
    baseInfoId: null,
    expectedCity: '',
    expectedCityCode: '',
    expectedIndustry: '',
    expectedIndustryCode: '',
    expectedPositions: '',
    expectedPositionsCode: '',
    jobType: null,
    salaryExpectationEnd: null,
    salaryExpectationStart: null,
    workEducational: null,
    isNews: null,
    isRecruit: null,
    distanceMeters: null,
    lon: null,
    lat: null, // 尾度
    workSalaryBegin: '',
    workSalaryEnd: '',
    sizeName: '',
    workExperienceStart: '',
    workExperienceEnd: '',
    positionName: '',
  },
  size: pageInfo.pageSize,
  page: pageInfo.pageNum,
})

const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}
// 去沟通item
const goChat = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      sendGreetingMessage(hxUserInfoVO.username, item)
    }
  } catch (error) {}
}
const tagList = ref([])

const goDetail = (id: any, companyId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId}`,
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 搜索
const confirm = () => {
  pagingRef.value.reload()
}

// 获取列表positionKey
const queryList = async () => {
  console.log('获取列表position')
  const res: any = await queryPositionByCompanyId(params)
  if (res.code === 0) {
    res.data?.list &&
      res.data.list.forEach((ele: any) => {
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryBegin =
          ele.workSalaryBegin === 0 ? '面议' : numberTokw(ele.workSalaryBegin + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '面议' : numberTokw(ele.workSalaryEnd + '')
        ele.distanceMeters = ele.distanceMeters
          ? Math.floor(parseInt(ele.distanceMeters) / 1000) + 'km'
          : ''
      })

    pagingRef.value.complete(res.data.list)
  }
}

onLoad(async () => {
  await uni.$onLaunched
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.salary {
  color: #ff8080 !important;
}

::v-deep .uni-scroll-view-content {
  text-align: left !important;
}
.stateType {
  padding: 0rpx 10rpx;
  margin-left: 10rpx;
  font-size: 20rpx;
  color: #888888;
  text-align: center;
  border: 1rpx solid #888888;

  border-radius: 6rpx;
}

.job-tag {
  padding: 0 20rpx;
  margin-right: 10rpx;
  font-size: 24rpx;
  color: #fff;
  background: #ff5151;
  border-radius: 20rpx 0rpx 20rpx 0rpx;
}

.page_left_1 {
  font-size: 24rpx !important;
  font-weight: 400;
  line-height: 44rpx;
  color: #666;
}

.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 10rpx;
  .bg_right-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 132rpx;
    height: 64rpx;
    text-align: center;
    background: #f0f3fd;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    &_icon {
      width: 32rpx;
      height: 32rpx;
    }
  }
  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 132rpx;
    height: 64rpx;
    text-align: center;
    background: #fff4f4;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    &_icon {
      width: 40rpx;
      height: 40rpx;
    }
    &_icon-1 {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 24rpx;
        font-weight: 400;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 22rpx;
        font-weight: 400;
        color: #666;
        // line-height: 44rpx;
      }
    }
  }
}

.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  padding-top: 0rpx !important;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 0rpx 10rpx;
    // margin: 14rpx 0;
    margin-top: 14rpx;
    margin-right: 24rpx;
    font-size: 22rpx;
    font-weight: 400;
    line-height: 44rpx;
    color: #888888;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.page_list {
  box-sizing: border-box;
  width: 100%;
  padding: 0 40rpx;
  //   margin-top: 50rpx;

  .page_flex {
    width: 100%;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .page_flex_colom {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;

      .page_flex_list {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .page_right_flex {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 5rpx 0;

          .page_left_1 {
            font-size: 24rpx !important;
            font-weight: 400;
            line-height: 44rpx;
            color: #666;
          }

          .page_right_distance {
            font-size: 22rpx;
            font-weight: 400;
            line-height: 44rpx;
            color: #888888;
          }
        }

        .page_left {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #333333;
        }

        .page_right {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #888888;
        }
      }
    }
  }
}

.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 50rpx;
  font-weight: 500;
  color: #000000;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 0;
  margin-bottom: 30rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 20rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;
        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}
</style>
