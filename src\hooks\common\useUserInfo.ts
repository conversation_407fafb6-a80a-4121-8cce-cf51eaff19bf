import { storeToRefs } from 'pinia'
import { useUserStore } from '@/store'

/** 用户信息hooks */
export const useUserInfo = () => {
  const { userInfo, isLoginEd: getUserIsLogin } = storeToRefs(useUserStore())
  const {
    setUserInfo: setUserIntel,
    setUserRoleType,
    setUserToken,
    getToken,
    getUserLoginTimeDiff,
    clearUserInfo,
  } = useUserStore()

  /** 用户信息 */
  const userIntel = computed(() => userInfo.value)
  /** 是否需要刷新token */
  const needRefreshToken = computed(() => getUserLoginTimeDiff() >= 1)
  /** 当前用户类型是否为企业 */
  const userRoleIsBusiness = computed(() => userIntel.value.type === 1)
  /** 当前用户是否实名 */
  const userRoleIsRealName = computed(() => !!userIntel.value.isAuth)
  return {
    /** 用户信息 */
    userIntel,
    /** 当前用户类型是否为企业 */
    userRoleIsBusiness,
    /** 当前用户是否实名 */
    userRoleIsRealName,
    /** 设置用户信息 */
    setUserIntel,
    /** 更新用户角色 */
    setUserRoleType,
    /** 获取用户是否登录 */
    getUserIsLogin,
    /** 设置用户令牌 */
    setUserToken,
    /** 获取用户令牌 */
    getToken,
    /** 清除用户信息 */
    clearUserInfo,
    /** 是否需要刷新token */
    needRefreshToken,
  }
}
