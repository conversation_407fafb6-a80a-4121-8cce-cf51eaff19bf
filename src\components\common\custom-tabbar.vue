<template>
  <wd-tabbar
    :model-value="name"
    active-color="#E65252"
    inactive-color="#999"
    @change="handleChange"
    safe-area-inset-bottom
    fixed
    placeholder
    custom-class="h-200rpx"
  >
    <wd-tabbar-item
      v-for="item in tabBarList"
      :key="`tabbar-${item.subName}`"
      :name="item.subName"
      :value="item.subName === 'news' ? totalUnreadCount : 0"
      :title="getTabTitle(item)"
      :custom-class="`flex-col ${tabBarActiveName === item.subName ? 'tabbar-is-active' : ''}`"
    >
      <template #icon="{ active }">
        <wd-img round height="44rpx" width="44rpx" :src="tabBarActive(active, item)" />
      </template>
    </wd-tabbar-item>
  </wd-tabbar>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { USER_TYPE } from '@/enum'
import homeNo from '@/static/tabbar/home_no.png'
import homeSelect from '@/static/tabbar/home_select.png'
import deepseekNo from '@/static/tabbar/deepseek_no.png'
import deepseekSelect from '@/static/tabbar/deepseek_select.png'
import newsNo from '@/static/tabbar/news_no.png'
import newsSelect from '@/static/tabbar/news_select.png'
import mineNo from '@/static/tabbar/mine_no.png'
import mineSelect from '@/static/tabbar/mine_select.png'
import searchNo from '@/static/tabbar/search_no.png'
import searchSelect from '@/static/tabbar/search_select.png'

defineProps({
  name: {
    type: String,
    default: 'home',
  },
})

const { userIntel, userRoleIsBusiness } = useUserInfo()
const { totalUnreadCount } = useIMConversation()
const tabBarActiveName = ref('home')
const tabBarLists = {
  [USER_TYPE.APPLICANT]: [
    {
      pagePath: '/pages/home/<USER>',
      text: '首页',
      subName: 'home',
      iconPath: homeNo,
      selectedIconPath: homeSelect,
    },
    {
      pagePath: '/pages/deepseek/index',
      text: '神马',
      subName: 'deepseek',
      iconPath: deepseekNo,
      selectedIconPath: deepseekSelect,
    },
    {
      pagePath: '/pages/news/index',
      text: '消息',
      subName: 'news',
      iconPath: newsNo,
      selectedIconPath: newsSelect,
    },
    {
      pagePath: '/pages/mine/index',
      text: '我的',
      subName: 'mine',
      iconPath: mineNo,
      selectedIconPath: mineSelect,
    },
  ],
  [USER_TYPE.HR]: [
    {
      pagePath: '/pages/home/<USER>',
      text: '首页',
      subName: 'home',
      iconPath: homeNo,
      selectedIconPath: homeSelect,
    },
    {
      pagePath: '/pages/deepseek/index',
      text: '搜索',
      subName: 'deepseek',
      iconPath: searchNo,
      selectedIconPath: searchSelect,
    },
    {
      pagePath: '/pages/news/index',
      text: '消息',
      subName: 'news',
      iconPath: newsNo,
      selectedIconPath: newsSelect,
    },
    {
      pagePath: '/pages/mine/index',
      text: '我的',
      subName: 'mine',
      iconPath: mineNo,
      selectedIconPath: mineSelect,
    },
  ],
}
const tabBarList = computed(() => tabBarLists[userIntel.value.type])
function tabBarActive(bool = false, item: AnyObject) {
  if (bool) {
    tabBarActiveName.value = item.subName
  }
  return bool ? item.selectedIconPath : item.iconPath
}
function handleChange({ value }) {
  const info = tabBarList.value.find((index) => index.subName === value)
  try {
    uni.switchTab({
      url: info.pagePath,
    })
  } catch (error) {}
}

function getTabTitle(item: { subName: string; text: string }) {
  const { subName, text } = item
  const isActive = tabBarActiveName.value === subName
  if (!isActive) return ''
  const isDeepSeek = subName === 'deepseek'
  const shouldShowDefaultDeepSeek = isDeepSeek && !userRoleIsBusiness.value
  return shouldShowDefaultDeepSeek ? 'deepseek' : text
}

uni.hideTabBar()
</script>

<style scoped lang="scss">
:deep(.wd-tabbar-item) {
  .wd-tabbar-item__body {
    flex-direction: row;
    gap: 18rpx;
    &-title {
      font-size: 28rpx;
    }
  }
}
.tabbar-is-active {
  :deep(.wd-tabbar-item__body) {
    box-sizing: border-box;
    padding: 12rpx 26rpx;
    background: #ffe5e5;
    border-radius: 76rpx;
  }
}
</style>
