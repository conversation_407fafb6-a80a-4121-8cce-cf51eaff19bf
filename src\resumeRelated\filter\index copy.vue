<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="page">
    <!-- 顶部搜索栏 -->
    <!-- <CustomNavBar title="筛选" bgColor="#fff"></CustomNavBar> -->
    <wd-navbar
      :bordered="false"
      fixed
      leftArrow
      placeholder
      safeAreaInsetTop
      custom-class="!bg-transparent"
      title="筛选"
      @click-left="handleClickLeft"
    ></wd-navbar>
    <!-- 分类容器 -->
    <view class="category-container">
      <!-- 左侧一级分类 -->
      <scroll-view
        class="left-category"
        scroll-y
        :scroll-top="leftScrollTop"
        :scroll-with-animation="true"
      >
        <view
          v-for="(item, index) in categories"
          :key="item.id"
          class="left-item"
          :class="{ active: currentCategory === index }"
          @click="changeCategory(index)"
        >
          {{ item.name }}
        </view>
      </scroll-view>

      <!-- 右侧二级分类 -->
      <scroll-view
        class="right-category"
        scroll-y
        :scroll-into-view="scrollIntoViewId"
        :scroll-with-animation="true"
        @scroll="handleRightScroll"
        :scroll-top="rightScrollTop"
      >
        <view
          v-for="(item, index) in categories"
          :key="item.id"
          class="right-section"
          :id="'section_' + index"
        >
          <!-- 二级分类标题 -->
          <view class="section-title">{{ item.name }}(单选)</view>

          <!-- 二级分类内容 -->
          <view class="section-content">
            <!-- 普通分类 -->
            <template v-if="item.children && item.children.length > 0">
              <view v-for="child in item.children" :key="child.id" class="child-category">
                <text class="child-name">{{ child.name }}</text>
              </view>
            </template>

            <!-- 标签式分类 -->
            <template v-if="item.tags && item.tags.length > 0">
              <view class="tag-container">
                <view
                  v-for="(tag, tagIndex) in item.tags"
                  :key="tagIndex"
                  class="tag-item"
                  :class="{
                    myStyleBox: isTagSelected(item.id, tag),
                  }"
                  @click="
                    handleTagClick(item.id, tag, item.tagData ? item.tagData[tagIndex] : null)
                  "
                >
                  {{ tag }}
                </view>
              </view>
            </template>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部筛选栏 -->
    <view class="filter-bar" v-if="showFilterBar">
      <view class="filter-buttons">
        <button class="reset-btn" @click="resetFilters">重置</button>
        <button class="confirm-btn" @click="confirmFilters">确定({{ selectedTagCount }})</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { queryById } from '@/interPost/common'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { numberTokw } from '@/utils/common'
import { ref, computed, nextTick, onMounted } from 'vue'
import { useResumeStore } from '@/store'
const resumeStore = useResumeStore()
const categories = ref<any[]>([])
const currentCategory = ref(0)
const leftScrollTop = ref(0)
const scrollIntoViewId = ref('section_0')
const rightScrollTop = ref(0)
const timer = ref<any>(null)
const selectedTags = ref<Record<string, any[]>>({})
const selectedTagIds = ref<Record<string, any[]>>({})
const showFilterBar = ref(true)
const isMultiSelect = ref(true)

const selectedTagCount = computed(() => {
  let count = 0
  for (const key in selectedTags.value) {
    count += selectedTags.value[key].length
  }
  return count
})
const handleClickLeft = () => {
  uni.navigateBack()
}
const initSelectedTagsFromStore = () => {
  const filterObj = resumeStore.fillterObg
  if (!filterObj) return

  if (filterObj.workEducational && categories.value.length > 0) {
    const eduCategory = categories.value.find((c) => c.id === 'workEducational')
    if (eduCategory && eduCategory.tagData) {
      const selectedEdu = eduCategory.tagData.find(
        (item: any) => Object.keys(item)[0] === filterObj.workEducational,
      )
      if (selectedEdu) {
        const tagName = Object.values(selectedEdu)[0]
        selectedTags.value.workEducational = [tagName]
        selectedTagIds.value.workEducational = [selectedEdu]
      }
    }
  }

  if (filterObj.sizeName && categories.value.length > 0) {
    const companyCategory = categories.value.find((c) => c.id === 'company')
    if (companyCategory && companyCategory.tagData) {
      const selectedCompany = companyCategory.tagData.find(
        (item: any) => Object.keys(item)[0] === filterObj.sizeName,
      )
      if (selectedCompany) {
        const tagName = Object.values(selectedCompany)[0]
        selectedTags.value.company = [tagName]
        selectedTagIds.value.company = [selectedCompany]
      }
    }
  }

  if (filterObj.workExperienceStart && categories.value.length > 0) {
    const expCategory = categories.value.find((c) => c.id === 'experience')
    if (expCategory && expCategory.tagData) {
      const selectedExp = expCategory.tagData.find(
        (item: any) =>
          item.workExperienceStart === filterObj.workExperienceStart &&
          item.workExperienceEnd === filterObj.workExperienceEnd,
      )
      if (selectedExp) {
        const tagName = selectedExp.workYear
        selectedTags.value.experience = [tagName]
        selectedTagIds.value.experience = [selectedExp]
      }
    }
  }

  if (filterObj.workSalaryBegin && categories.value.length > 0) {
    const salaryCategory = categories.value.find((c) => c.id === 'salary')
    if (salaryCategory && salaryCategory.tagData) {
      const selectedSalary = salaryCategory.tagData.find(
        (item: any) =>
          item.workSalaryBegin === filterObj.workSalaryBegin &&
          item.workSalaryEnd === filterObj.workSalaryEnd,
      )
      if (selectedSalary) {
        const tagName =
          numberTokw(selectedSalary.workSalaryBegin) +
          '-' +
          numberTokw(selectedSalary.workSalaryEnd)
        selectedTags.value.salary = [tagName]
        selectedTagIds.value.salary = [selectedSalary]
      }
    }
  }
}

onMounted(() => {
  initData()
})

const initData = async () => {
  await getEducational()
  await getCompany()
  await getExperiencel()
  await getSalary()
  rightScrollPositions.value = new Array(categories.value.length).fill(0)
  initSelectedTagsFromStore()
}

const rightScrollPositions = ref<number[]>([])

const getEducational = async () => {
  const res: any = await queryById({ id: 10 })
  if (res.code === 0) {
    categories.value.push({
      id: 'workEducational',
      name: '学历要求',
      tags: res.data.map((v) => Object.values(v)[0]),
      tagData: res.data,
      multiSelect: false,
    })
  }
}

const getCompany = async () => {
  const res: any = await queryById({ id: 100 })
  if (res.code === 0) {
    categories.value.push({
      id: 'company',
      name: '公司规模',
      tags: res.data.map((v) => Object.values(v)[0]),
      tagData: res.data,
      multiSelect: false,
    })
  }
}

const getExperiencel = async () => {
  const res: any = await queryById({ id: 102 })
  if (res.code === 0) {
    categories.value.push({
      id: 'experience',
      name: '经验要求',
      tags: res.data.map((v) => v.workYear),
      tagData: res.data,
      multiSelect: false,
    })
  }
}

const getSalary = async () => {
  const res: any = await queryById({ id: 104 })
  if (res.code === 0) {
    res.data.forEach((v) => {
      v.salary = numberTokw(v.workSalaryBegin) + '-' + numberTokw(v.workSalaryEnd)
    })
    categories.value.push({
      id: 'salary',
      name: '薪资范围',
      tags: res.data.map((v) => v.salary),
      tagData: res.data,
      multiSelect: false,
    })
  }
}

// 修改后的changeCategory函数
const changeCategory = (index: number) => {
  if (currentCategory.value === index) return

  // 直接更新当前选中项
  currentCategory.value = index
  scrollIntoViewId.value = 'section_' + index

  // 计算左侧滚动位置
  nextTick(() => {
    const query = uni.createSelectorQuery()
    query.select('.left-item.active').boundingClientRect()
    query.select('.left-category').boundingClientRect()
    query.exec((res) => {
      if (res[0] && res[1]) {
        const activeItem = res[0]
        const container = res[1]
        const scrollTop =
          activeItem.top - container.top - (container.height - activeItem.height) / 2
        leftScrollTop.value = scrollTop
      }
    })
  })

  // 修复右侧滚动问题
  nextTick(() => {
    rightScrollTop.value = rightScrollPositions.value[index] || 0
  })
}

const handleRightScroll = (e: any) => {
  if (timer.value) clearTimeout(timer.value)

  timer.value = setTimeout(() => {
    const scrollTop = e.detail.scrollTop
    rightScrollPositions.value[currentCategory.value] = scrollTop

    const query = uni.createSelectorQuery()

    categories.value.forEach((item, index) => {
      query.select('#section_' + index).boundingClientRect()
    })

    query.exec((res) => {
      if (res && res.length > 0) {
        for (let i = 0; i < res.length; i++) {
          if (res[i] && res[i].top <= 100 && res[i].bottom > 100) {
            // 只有当滚动距离足够时才更新当前分类
            if (Math.abs(currentCategory.value - i) > 0) {
              currentCategory.value = i
            }
            rightScrollPositions.value[i] = scrollTop
            break
          }
        }
      }
    })
  }, 100)
}

const isTagSelected = (categoryId: string, tag: string) => {
  return selectedTags.value[categoryId] && selectedTags.value[categoryId].includes(tag)
}

const handleTagClick = (categoryId: string, tag: string, tagId: any) => {
  const category = categories.value.find((item) => item.id === categoryId)
  const isMultiSelect = category ? category.multiSelect : isMultiSelect.value

  if (!selectedTags.value[categoryId]) {
    selectedTags.value[categoryId] = []
    selectedTagIds.value[categoryId] = []
  }

  if (isMultiSelect) {
    const index = selectedTags.value[categoryId].indexOf(tag)
    if (index === -1) {
      selectedTags.value[categoryId].push(tag)
      selectedTagIds.value[categoryId].push(tagId)
    } else {
      selectedTags.value[categoryId].splice(index, 1)
      selectedTagIds.value[categoryId].splice(index, 1)
    }
  } else {
    if (selectedTags.value[categoryId][0] === tag) {
      selectedTags.value[categoryId] = []
      selectedTagIds.value[categoryId] = []
    } else {
      selectedTags.value[categoryId] = [tag]
      selectedTagIds.value[categoryId] = [tagId]
    }
  }

  if (selectedTags.value[categoryId].length === 0) {
    delete selectedTags.value[categoryId]
    delete selectedTagIds.value[categoryId]
  }
}

const removeTag = (categoryId: string, tag: string) => {
  if (selectedTags.value[categoryId]) {
    const index = selectedTags.value[categoryId].indexOf(tag)
    if (index !== -1) {
      selectedTags.value[categoryId].splice(index, 1)
      selectedTagIds.value[categoryId].splice(index, 1)

      if (selectedTags.value[categoryId].length === 0) {
        delete selectedTags.value[categoryId]
        delete selectedTagIds.value[categoryId]
      }
    }
  }
}

const clearAllTags = () => {
  selectedTags.value = {}
  selectedTagIds.value = {}
  const obj = {
    workSalaryBegin: '',
    workSalaryEnd: '',
    sizeName: '',
    workEducational: '',
    workExperienceStart: '',
    workExperienceEnd: '',
  }
  resumeStore.setfillterObg(obj)
  uni.navigateBack({
    delta: 1,
    success() {
      uni.$emit('refresh-a-page') // 触发刷新事件
    },
  })
}

const resetFilters = () => {
  clearAllTags()
}

const confirmFilters = () => {
  const obj = {
    workSalaryBegin: '',
    workSalaryEnd: '',
    sizeName: '',
    workEducational: '',
    workExperienceStart: '',
    workExperienceEnd: '',
  }

  if (selectedTagIds.value.salary && selectedTagIds.value.salary[0]) {
    obj.workSalaryBegin = selectedTagIds.value.salary[0].workSalaryBegin
    obj.workSalaryEnd = selectedTagIds.value.salary[0].workSalaryEnd
  }

  if (selectedTagIds.value.company && selectedTagIds.value.company[0]) {
    obj.sizeName = Object.keys(selectedTagIds.value.company[0])[0]
  }

  if (selectedTagIds.value.workEducational && selectedTagIds.value.workEducational[0]) {
    obj.workEducational = Object.keys(selectedTagIds.value.workEducational[0])[0]
  }

  if (selectedTagIds.value.experience && selectedTagIds.value.experience[0]) {
    obj.workExperienceStart = selectedTagIds.value.experience[0].workExperienceStart
    obj.workExperienceEnd = selectedTagIds.value.experience[0].workExperienceEnd
  }

  resumeStore.setfillterObg(obj)
  uni.navigateBack({
    delta: 1,
    success() {
      uni.$emit('refresh-a-page') // 触发刷新事件
    },
  })
}
</script>

<style lang="scss" scoped>
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}
.myStyleBox {
  position: relative;
  border: 1px solid #1160ff;
}
.myStyleBox::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('@/loginSetting/img/Mask_group(2).png');
}
.search-bar {
  padding: 15rpx 30rpx;
  background-color: #fff;
}

.category-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-category {
  box-sizing: border-box;
  width: 160rpx;
  height: 100%;
  background-color: #f5f4f4;
}

.left-item {
  position: relative;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 104rpx;
  padding: 0 10rpx;
  font-size: 26rpx;
  line-height: 1.4;
  color: #333;
  text-align: center;
}

.left-item.active {
  font-weight: bold;
  color: #1160ff;
  background-color: #fff;
}

.left-item.active::before {
  position: absolute;
  top: 30rpx;
  bottom: 30rpx;
  left: 0;
  width: 6rpx;
  content: '';
  border-radius: 0 6rpx 6rpx 0;
}

.right-category {
  box-sizing: border-box;
  flex: 1;
  height: 100%;
  padding: 0 20rpx;
  background-color: #fff;
}

.section-title {
  height: 104rpx;
  padding-left: 10rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 104rpx;
  color: #333;
}

.section-content {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.child-category {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 45%;
  padding: 0 10rpx;
  margin-bottom: 30rpx;
}

.child-name {
  font-size: 24rpx;
  line-height: 1.4;
  color: #333;
  text-align: center;
}

.tag-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
}

.tag-item {
  width: 45%;
  padding: 10rpx 24rpx;
  margin: 0 20rpx 20rpx 0;
  font-size: 26rpx;
  color: #333;
  text-align: center;
  background-color: #fff;
  border: 1rpx solod transparent;
  border-radius: 10rpx;
  box-shadow: 0 8rpx 28rpx 0 rgba(0, 0, 0, 0.1);
}

.filter-bar {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-buttons {
  display: flex;
  padding: 20rpx 30rpx;
}

.reset-btn,
.confirm-btn {
  flex: 1;
  height: 80rpx;
  font-size: 30rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.reset-btn {
  margin-right: 20rpx;
  color: #333;
  background-color: #f5f5f5;
}

.confirm-btn {
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
}
</style>
