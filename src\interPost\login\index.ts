import { POST } from '@/service'

// 加密接口
export const msgList = () => {
  return POST('/easyzhipin-api/msg/list')
}
// 手机号检测(检测是否需要验证等信息)接口
export const phoneCheck = (data: any) => {
  return POST('/easyzhipin-api/login/phoneCheck', data)
}

// 发送验证码
export const phonesendSms = (data: any) => {
  return POST('/easyzhipin-api/login/sendSms', data)
}

// 手机号验证码登录接口
export const subphoneLogin = (data: any) => {
  return POST('/easyzhipin-api/login/phoneLogin', data)
}

// 用户相关最新状态接口
export const newsInfo = (data?: any) => {
  return POST('/easyzhipin-api/user/newsInfo', data)
}

// 退出登录
export const logout = () => {
  return POST('/easyzhipin-api/login/logout')
}
