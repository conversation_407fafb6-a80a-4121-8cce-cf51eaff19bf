export interface imDomainInfoInt {
  /** appKey */
  appKey: string
  /** rest国内1区 */
  basePath: string
  /** rest国内2区  */
  basePathSta: string
  /** websocket国内1区 */
  baseWsPath: string[]
  /** websocket国内2区 */
  baseWsPathSta: string[]
}
export interface imCreateAccountDataInt extends Pick<Api.User.IUserInfo, 'type'> {}
export interface imCreateAccountInt {
  /** 用户名 */
  username: string
  /** 用户授权token */
  accessToken: string
}
