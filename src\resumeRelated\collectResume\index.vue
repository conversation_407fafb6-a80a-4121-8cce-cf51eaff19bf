<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" v-model="pageData" @query="queryList" :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="收藏"></CustomNavBar>
    </template>
    <view class="page_list">
      <view class="page_flex" v-for="(item, index) in pageData" :key="index">
        <view class="page_flex_colom" @click="goDetail">
          <view class="page_flex_list">
            <view class="page_left">
              {{ item.positionName }}
            </view>
            <view class="page_right salary">
              {{ item.workSalaryBegin }}-{{ item.workSalaryEnd }}
            </view>
          </view>
          <view class="page_flex_list">
            <view class="page_left_1">某某某某传媒有限公司·不需要融资</view>
            <view class="page_right_flex">
              <wd-icon name="location" size="14px" color="#999"></wd-icon>
              <view class="page_right_distance">400m</view>
            </view>
          </view>
          <view class="bg_flex">
            <view class="bg_box">本科</view>
            <view class="bg_box">1-3年</view>
            <view class="bg_box">英语4级</view>
            <view class="bg_box">健康饮食管理证书</view>
          </view>
          <view class="bg_end">
            <view class="bg_left">
              <image class="bg_left_icon" src="/static/img/1.jpg" mode="aspectFill"></image>
              <view class="bg_left_flex">
                <view class="bg_left_name">张先生·hr人事经理</view>
                <view class="bg_left_date">三日内活跃</view>
              </view>
            </view>
            <view class="bg_right" @click.stop="goChat">
              <image
                class="bg_right_icon"
                src="/static/images/home/<USER>"
                mode="aspectFill"
              ></image>
            </view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
// import { usePaging } from '@/hooks/common/usePaging'
import { getqueryList } from '@/interPost/home'
import { numberTokw } from '@/utils/common'

defineOptions({
  name: 'Home',
})
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const form = ref({
  title: '',
})
const params = reactive({
  orderBy: {},
  entity: {
    baseInfoId: null,
    expectedCity: '',
    expectedCityCode: '',
    expectedIndustry: '',
    expectedIndustryCode: '',
    expectedPositions: '',
    expectedPositionsCode: '',
    jobType: 1,
    salaryExpectationEnd: null,
    salaryExpectationStart: null,
    workEducational: null,
  },
  size: pageInfo.pageSize,
  page: pageInfo.pageNum,
})
const selectTag = ref(0)
const selectIndex = ref(0)
const typeList = ref([
  {
    name: '推荐',
    value: '1',
  },
  {
    name: '附近',
    value: '2',
  },
  {
    name: '最新',
    value: '3',
  },
  {
    name: '急招',
    value: '4',
  },
])

const tagList = ref([
  {
    name: '电商运行',
    value: '1',
  },
  {
    name: '直播运营',
    value: '2',
  },
  {
    name: '客服运营',
    value: '3',
  },
])

const goFilter = () => {
  uni.navigateTo({
    url: '/resumeRelated/filter/index',
  })
}
const goDetail = () => {
  uni.navigateTo({
    url: '/resumeRelated/jobDetail/index',
  })
}

const queryList = async () => {
  await uni.$onLaunched
  const res: any = await getqueryList(params)
  console.log(res, 'res====')
  if (res.code === 0) {
    res.data.list.forEach((ele: any) => {
      ele.workSalaryBegin = numberTokw(ele.workSalaryBegin + '')
      ele.workSalaryEnd = numberTokw(ele.workSalaryEnd + '')
    })
    pagingRef.value.complete(res.data.list)
  }
}
onLoad(async () => {
  await nextTick()
  pagingRef.value.reload()
})
const handType = (index: any) => {
  selectTag.value = index
}
const goChat = () => {
  uni.navigateTo({
    url: '/chartPage/chartPage/index',
  })
}
const handSelect = (index: any) => {
  selectIndex.value = index
}
onMounted(() => {
  pagingRef.value.setLocalPaging([
    {
      title: '视频运营总监电商运营',
      salary: '4.5k-12k·14薪',
      conpany: '某某某某传媒有限公司·不需要融资',
      postion: '400m',
      xl: '本科',
      workName: '1-3年',
      zs: '英语4级',
      hh: '健康饮食管理证书',
      name: '张先生·hr人事经理',
      hy: '三日内活跃',
    },
    {
      title: '视频运营总监电商运营',
      salary: '4.5k-12k·14薪',
      conpany: '某某某某传媒有限公司·不需要融资',
      postion: '400m',
      xl: '本科',
      workName: '1-3年',
      zs: '英语4级',
      hh: '健康饮食管理证书',
      name: '张先生·hr人事经理',
      hy: '三日内活跃',
    },
    {
      title: '视频运营总监电商运营',
      salary: '4.5k-12k·14薪',
      conpany: '某某某某传媒有限公司·不需要融资',
      postion: '400m',
      xl: '本科',
      workName: '1-3年',
      zs: '英语4级',
      hh: '健康饮食管理证书',
      name: '张先生·hr人事经理',
      hy: '三日内活跃',
    },
  ])
})
</script>

<style lang="scss" scoped>
.salary {
  color: #4399ff !important;
}
.content_search-p {
  padding: 30rpx 40rpx;
}

.content_search-p-t {
  padding: 0rpx 40rpx 20rpx;
}

.page_left_1 {
  font-size: 22rpx !important;
  font-weight: 400;
  line-height: 44rpx;
  color: #888888;
}

.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 10rpx;

  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 152rpx;
    height: 64rpx;
    text-align: center;
    background: #fff4f4;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    &_icon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 22rpx;
        font-weight: 400;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 20rpx;
        font-weight: 400;
        color: #999999;
        // line-height: 44rpx;
      }
    }
  }
}

.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 100%;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 4rpx 40rpx;
    // margin: 14rpx 0;
    margin-top: 14rpx;
    margin-right: 22rpx;
    font-size: 22rpx;
    font-weight: 400;
    line-height: 44rpx;
    color: #888888;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.page_list {
  box-sizing: border-box;
  width: 100%;
  padding: 20rpx 40rpx 0rpx;
  // margin-bottom: 200rpx;

  .page_flex {
    width: 100%;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .page_flex_colom {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;

      .page_flex_list {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        justify-content: space-between;
        width: 100%;

        .page_right_flex {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 5rpx 0;

          .page_left_1 {
            font-size: 22rpx !important;
            font-weight: 400;
            line-height: 44rpx;
            color: #888888;
          }

          .page_right_distance {
            font-size: 22rpx;
            font-weight: 400;
            line-height: 44rpx;
            color: #888888;
          }
        }

        .page_left {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #333333;
        }

        .page_right {
          font-size: 28rpx;
          font-weight: 600;
          line-height: 44rpx;
          color: #888888;
        }
      }
    }
  }
}

.navigation-bar {
  // position: fixed;
  // top: 0;
  // left: 0;
  // right: 0;
  // z-index: 999;
}

.content_search_list_flex {
  display: flex !important;
  flex-direction: row;
  align-items: center;
  justify-content: space-between !important;
}

.navigation-bar-betwween {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40rpx;
}

.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 60rpx;
  font-weight: 500;
  color: #000000;
}

.content_list_right {
  display: flex;
  flex-direction: row;
  align-items: center;

  .content_list_adress {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 24rpx;
    font-weight: 400;
    color: #555555;
  }

  .content_list_icon {
    display: flex;
    align-items: center;
    padding: 16rpx 10rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
    ._img {
      width: 50rpx;
      height: 50rpx;
    }
  }
}

.content_list_left_for {
  display: flex;
  flex-direction: row;
  width: 100%;
  padding-right: 50rpx;
}

.content_list_left_color {
  margin-bottom: -10rpx;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 44rpx;
  color: #000000;
}

.content_list {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;

  .content_list_left {
    // width: 80%;
    display: flex;
    flex-direction: row;
    align-items: center;

    .content_list_left_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 10rpx 20rpx;
      white-space: nowrap;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_list_for {
        .content_list_border {
          // 	font-size: 24rpx;
          // font-weight: 500;
          // font-size: 24rpx;
          // color: #333333;
          // padding: 8rpx 20rpx;
          // 	line-height: 44rpx;
          // 	text-align: center;
          // 	background: linear-gradient( 90deg, #FFC2C2 0%, #DDDCFF 100%);
          // 	border-radius: 16rpx 16rpx 16rpx 16rpx;
        }

        .content_list_border_1 {
          padding-left: 30rpx;
          // font-size: 24rpx;
          // color: #3E3E56;
          // line-height: 44rpx;
          // text-align: center;
        }
      }
    }
  }
}

.select_border {
  padding: 11rpx 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #333333;
  text-align: center;
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
}

.select_noBorder {
  font-size: 24rpx;
  line-height: 44rpx;
  color: #3e3e56;
  text-align: center;
}

.content_list_left_xian {
  width: 58rpx;
  height: 8rpx;
  font-weight: bold;
  background: #ff9191;
  border-radius: 2rpx 2rpx 2rpx 2rpx;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx;
  padding-bottom: 0;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 10rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;
        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.content {
  // height: 100%;
  // background-color: rgba(244, 244, 244, 1);
  width: 100%;
}

.start_ICON {
  ._icon {
    width: 56rpx;
    height: 56rpx;
  }
}
</style>
