<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="containner bg-img">
    <CustomNavBar title="期望职位"></CustomNavBar>
    <view class="content_flex">
      <view class="content_search">
        <view class="content_search_bg">
          <view class="content_search_left">
            <image src="/static/img/sousuo-2_1 (3).png" mode="aspectFill"></image>
          </view>
          <view class="content_search_right">
            <wd-input no-border placeholder="搜索您想要的内容" v-model="keyword"></wd-input>
          </view>
        </view>
      </view>
    </view>

    <!-- 主列表容器 -->
    <view class="page-list">
      <!-- 左侧父级分类 -->
      <scroll-view
        scroll-y
        :style="{ height: 'calc(100vh - ' + scrollViewHeight + 'px)' }"
        class="page-list-left"
      >
        <view v-for="(parentName, pIndex) in positionDataList" :key="pIndex">
          <view
            :class="[
              activeFIndex === pIndex ? 'activeBg' : 'normalBg',
              pIndex === activeFIndex - 1 ? 'prev-selected' : '',
              pIndex === activeFIndex + 1 ? 'next-selected' : '',
              'page-list-left-text',
            ]"
            class="page-list-left-text"
            @click="activeF(pIndex)"
          >
            {{ parentName.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 右侧子级及孙子级内容 -->
      <scroll-view
        scroll-y
        :style="{ height: 'calc(100vh - ' + scrollViewHeight + 'px)' }"
        class="page-list-right"
      >
        <view v-for="(child, cIndex) in currentChildren" :key="cIndex">
          <view class="page-list-right-p">
            <view class="page-list-right-title">{{ child.name }}</view>
            <view class="page-tag-list">
              <view
                v-for="(grandchild, gIndex) in child.subLevelModelList"
                :key="gIndex"
                class="tag-select-r"
                :class="grandchild.active ? 'myStyle-box' : ''"
                @click="changeActive(cIndex, gIndex)"
              >
                {{ grandchild.name }}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import positionData from '@/utils/json/getCityShowPosition.json'
import { getCustomBar } from '@/utils/storege'
import { useLoginStore } from '@/store'
import { onShow } from '@dcloudio/uni-app'

// vuex数据
const loginStore = useLoginStore()
const keyword = ref('')
const activeFIndex = ref(0)
const positionDataList = ref([])
const scrollViewHeight = ref(0)

const currentChildren = computed(() => {
  return positionDataList.value[activeFIndex.value]?.subLevelModelList || []
})

const activeF = (index) => {
  activeFIndex.value = index
}

const changeActive = (cIndex, gIndex) => {
  // 先重置所有选项
  positionDataList.value.forEach((parent) => {
    parent.subLevelModelList &&
      parent.subLevelModelList.forEach((child) => {
        child.subLevelModelList &&
          child.subLevelModelList.forEach((grandchild) => {
            grandchild.active = false
          })
      })
  })

  // 获取当前点击项
  const target =
    positionDataList.value[activeFIndex.value].subLevelModelList[cIndex].subLevelModelList[gIndex]
  // 切换当前项选中状态
  target.active = !target.active
  const positionObj = {
    expectedPositions:
      positionDataList.value[activeFIndex.value].subLevelModelList[cIndex].subLevelModelList[gIndex]
        .name,
    expectedPositionsCode:
      positionDataList.value[activeFIndex.value].subLevelModelList[cIndex].subLevelModelList[gIndex]
        .code,
  }
  console.log(positionObj, 'positionObj=====')
  loginStore.setpositionData(positionObj)
  uni.navigateBack()
}

// 反显选中的数据
const initSelectedData = () => {
  const selectedPosition = loginStore.positionObj
  console.log(selectedPosition, 'selectedPosition====')
  if (!selectedPosition || !selectedPosition.expectedPositionsCode) return
  positionDataList.value.forEach((parent) => {
    parent.subLevelModelList &&
      parent.subLevelModelList.forEach((child) => {
        child.subLevelModelList &&
          child.subLevelModelList.forEach((grandchild: AnyObject) => {
            if (grandchild.code === selectedPosition.expectedPositionsCode) {
              grandchild.active = true
              // 找到对应的父级索引，自动滚动到对应位置
              activeFIndex.value = positionDataList.value.findIndex((item) =>
                item.subLevelModelList?.some((childItem) =>
                  childItem.subLevelModelList?.some(
                    (grandchildItem) =>
                      grandchildItem.code === selectedPosition.expectedPositionsCode,
                  ),
                ),
              )
            }
          })
      })
  })
}

onLoad(() => {
  scrollViewHeight.value = getCustomBar() + 75
  const list = positionData.zpData.position
  list.forEach((parent) => {
    parent.subLevelModelList &&
      parent.subLevelModelList.forEach((child) => {
        child.subLevelModelList &&
          child.subLevelModelList.forEach((grandchild: AnyObject) => {
            grandchild.active = false
          })
      })
  })
  positionDataList.value = list
})

onShow(() => {
  initSelectedData()
})
</script>

<style lang="scss" scoped>
::v-deep .wd-input {
  width: 100%;
  background-color: transparent !important;
}
::v-deep .wd-input__placeholder {
  font-size: 28rpx !important;
  color: #fff !important;
}
::v-deep .wd-input__inner {
  font-size: 28rpx !important;
  font-weight: 500;
  color: #fff !important;
}
.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 40rpx 40rpx;
  padding-bottom: 20rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 30rpx;
      color: #fff;
      background: rgba(61, 61, 61, 0.34);
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 10%;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.containner-select-list {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 0 !important;
}

.myStyle-box::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('../img/Mask_group(2).png');
}

.page-list-right-title {
  padding-bottom: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.page-list-right-p {
  padding: 30rpx 20rpx 0rpx !important;
  margin-bottom: 30rpx;
}

.activeBg {
  font-weight: 500;
  color: #1160ff;
  background: transparent;
}

.tag-select {
  position: relative;
  width: 200rpx;
  padding: 8rpx 0;
  color: #555;
  text-align: center;
  background-color: #fff;
  border: 1px solid #1160ff;
  border-radius: 10rpx;
}

.normalBg {
  background: #e8e8e8;
  // border: 1px solid transparent;
}

.u-searchs {
  padding: 0 46rpx;
  border-bottom: 1rpx solid $uni-border-b-color;
}

.page-list {
  display: flex;
  padding: 0;

  &-left {
    width: 230rpx;
    // padding: 20rpx 0 0;

    &-text {
      padding: 30rpx 10rpx 30rpx 10rpx;
      font-size: 28rpx;
      text-align: center;
      transition: all 0.3s;

      &.prev-selected {
        border-bottom-right-radius: 20rpx;
      }

      &.next-selected {
        border-top-right-radius: 20rpx;
      }
    }
  }

  &-right {
    flex: 1;

    &-p {
      padding: 40rpx 30rpx;

      &-title {
        margin-bottom: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }
}

.page-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
  justify-content: space-between;

  .tag-select-r {
    position: relative;
    width: 225rpx;
    padding: 12rpx 4rpx !important;
    margin-bottom: 10rpx;
    font-size: 28rpx;
    color: #333;
    text-align: center;
    background: #f5f5f5;
    border-radius: 10rpx;
    transition: all 0.3s;

    &.myStyle-box {
      border: 1px solid #1160ff;
    }
  }
}
</style>
