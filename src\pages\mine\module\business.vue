<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <wd-navbar :bordered="false" safe-area-inset-top placeholder custom-class="!bg-transparent">
        <template #left>
          <wd-img
            :src="identitySwitchingImg"
            width="58rpx"
            height="58rpx"
            @click="chanangeIdentity"
          />
        </template>
        <template #right>
          <view class="flex items-center">
            <view class="m-r-20rpx text-28rpx c-#333" @click="logoutBtm">退出</view>
            <wd-img :src="setupImg" width="58rpx" height="58rpx" />
          </view>
        </template>
      </wd-navbar>
    </template>
    <view class="px-50rpx">
      <view class="flex items-center py-32rpx border-b-1px border-b-solid border-b-[#BDBDBD]">
        <view class="flex items-center gap-24rpx flex-1">
          <wd-img width="110rpx" height="110rpx" round src="/static/verify/1.jpeg" />
          <view class="flex flex-col gap-14rpx">
            <view class="flex items-center line-clamp-1">
              <text class="c-#333333 text-32rpx font-500">张女士</text>
              <text>&nbsp;·&nbsp;</text>
              <text class="c-#555555 text-28rpx">HR·部门主管</text>
            </view>
            <text class="c-#333333 text-28rpx line-clamp-1">重庆奇瑞科技有限公司</text>
          </view>
        </view>
        <wd-icon name="arrow-right" color="#555555" size="40rpx" />
      </view>
      <view class="flex flex-col gap-60rpx mt-32rpx">
        <view class="grid grid-cols-4 mx--50rpx">
          <view
            class="flex flex-col items-center gap-10px"
            v-for="(item, key) in newsList"
            :key="`news-${key}`"
          >
            <text class="c-#555555 text-24rpx">{{ item.name }}</text>
            <text class="c-#333333 text-36rpx font-bold">{{ item.num }}</text>
          </view>
        </view>
        <view class="grid grid-cols-4 mx--10rpx gap-30rpx">
          <view
            class="flex flex-col items-center gap-10px"
            v-for="(item, key) in selectList"
            :key="`select-${key}`"
            @click="changeHandel(key)"
          >
            <view class="relative w-58rpx h-58rpx flex p-6rpx">
              <wd-img width="100%" height="100%" :src="`/static/mine/business/${item.icon}.png`" />
              <view class="absolute left-0 top-0 w-5px h-1px bg-#333"></view>
              <view class="absolute left-0 top-0 w-1px h-5px bg-#333"></view>
              <view class="absolute right-0 top-0 w-5px h-1px bg-#333"></view>
              <view class="absolute right-0 top-0 w-1px h-5px bg-#333"></view>
              <view class="absolute left-0 bottom-0 w-10rpx h-2rpx bg-#333"></view>
              <view class="absolute left-0 bottom-0 w-1px h-5px bg-#333"></view>
              <view class="absolute right-0 bottom-0 w-10rpx h-2rpx bg-#333"></view>
              <view class="absolute right-0 bottom-0 w-1px h-5px bg-#333"></view>
            </view>
            <text class="c-#000000 text-24rpx">{{ item.title }}</text>
          </view>
        </view>
        <view
          class="h-196rpx bg-cover bg-no-repeat bg-center border-rd-40rpx flex flex-col justify-center box-border pl-32rpx pr-134rpx gap-16rpx"
          :style="{ backgroundImage: `url(${bombCardBg})` }"
        >
          <wd-img :src="bombCardTitle" width="154rpx" height="46rpx" />
          <text class="text-22rpx c-#000000 line-clamp-2">
            使用规则使用规则使用规则使用规则使用
          </text>
        </view>
      </view>
    </view>
    <template #bottom>
      <customTabbar name="mine" />
    </template>
  </z-paging>
  <orbital-menu :items="selectList" />
</template>

<script lang="ts" setup>
import customTabbar from '@/components/common/custom-tabbar.vue'
import orbitalMenu from '@/components/common/orbital-menu.vue'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
import setupImg from '@/static/common/setup.png'
import bombCardTitle from '@/static/mine/business/bomb-card-title.png'
import bombCardBg from '@/static/mine/business/bomb-card-bg.png'
import { logout } from '@/interPost/login'
import { clearStorageSync } from '@/utils/storege'

defineOptions({
  name: 'MineBusiness',
})
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const newsList = reactive([
  {
    name: '面试',
    num: 26,
  },
  {
    name: '沟通过',
    num: 1039,
  },
  {
    name: '被收藏',
    num: 20,
  },
  {
    name: '不合适',
    num: 789,
  },
])
const selectList = reactive([
  {
    icon: 'job-data',
    title: '发布岗位',
  },
  {
    icon: 'company-homepage',
    title: '公司主页',
  },
  {
    icon: 'address-management',
    title: '地址管理',
  },
  {
    icon: 'my-props',
    title: '我的道具',
  },
  {
    icon: 'rules-center',
    title: '规则中心',
  },
  {
    icon: 'wallet-invoices',
    title: '钱包发票',
  },
  {
    icon: 'my-customer-service',
    title: '我的客服',
  },
  {
    icon: 'privacy-policy',
    title: '隐私协议',
  },
])
const logoutBtm = async () => {
  const res: any = await logout()
  if (res.code === 0) {
    loginStore.sethomeJobAvtive(0)
    loginStore.sethomeCity1({})
    loginStore.sethomeCity2({})
    loginStore.sethomeCity3({})
    clearStorageSync()
    uni.reLaunch({
      url: '/pages/login/index',
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 切换身份
const chanangeIdentity = () => {
  uni.navigateTo({
    url: '/setting/IdentitySwitching/index',
  })
}
// 支付
const changeHandel = (index: number) => {
  console.log(index, 'index===')
  if (index === 0) {
    uni.navigateTo({
      url: '/paymentRelated/positionPay/index',
    })
  }
}
</script>

<style lang="scss" scoped>
//
</style>
