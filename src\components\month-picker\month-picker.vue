<template>
  <view class="month-picker-container">
    <picker-view
      class="picker-view"
      :value="pickerValue"
      @change="handleChange"
      :indicator-style="indicatorStyle"
    >
      <!-- 年份列 -->
      <picker-view-column>
        <view class="picker-item" v-for="year in yearRange" :key="year">{{ year }}年</view>
      </picker-view-column>

      <!-- 月份列（动态生成） -->
      <picker-view-column>
        <view class="picker-item" v-for="month in months" :key="month">
          {{ String(month).padStart(2, '0') }}月
        </view>
      </picker-view-column>
    </picker-view>
  </view>
</template>

<script setup lang="ts">
const props = defineProps({
  value: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['input', 'change'])

const indicatorStyle = `
  height: 54px; 
  line-height: 54px;
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
`

const currentDate = new Date()
const latestDate = new Date()
latestDate.setFullYear(latestDate.getFullYear() - 16)
latestDate.setDate(0)

const startYear = currentDate.getFullYear() - 80
const endYear = latestDate.getFullYear()

const yearRange = Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i)
const latestYear = latestDate.getFullYear()
const latestMonth = latestDate.getMonth() // 0-based
const pickerValue = ref([0, 0])

const months = computed(() => {
  const selectedYear = yearRange[pickerValue.value[0]]
  if (selectedYear < latestYear) {
    return Array.from({ length: 12 }, (_, i) => i + 1) // 1-12
  }
  return Array.from({ length: latestMonth + 1 }, (_, i) => i + 1) // 1-latestMonth+1
})

// 初始化或更新picker值
const updatePickerValue = (year, month) => {
  const yearIndex = yearRange.findIndex((y) => y === year)
  if (yearIndex === -1) return

  // 确保月份不超过可选范围
  const maxMonth = year === latestYear ? latestMonth + 1 : 12
  const monthIndex = Math.min(month, maxMonth) - 1 // 转换为0-based

  pickerValue.value = [yearIndex, Math.max(0, monthIndex)]
}

const initDefaultValue = () => {
  let year, month

  if (props.value) {
    const [y, m] = props.value.split('-').map(Number)
    year = y
    month = m
  } else {
    year = latestYear
    month = latestMonth + 1 // 使用1-based月份
  }

  // 验证范围
  if (year > latestYear || (year === latestYear && month > latestMonth + 1)) {
    year = latestYear
    month = latestMonth + 1
  }

  updatePickerValue(year, month)
  emitDate(year, month)
}

const handleChange = (e) => {
  const [yearIndex, monthIndex] = e.detail.value
  const selectedYear = yearRange[yearIndex]

  // 确保月份不超过限制
  let adjustedMonthIndex = monthIndex
  if (selectedYear === latestYear) {
    adjustedMonthIndex = Math.min(monthIndex, latestMonth)
  } else {
    adjustedMonthIndex = Math.min(monthIndex, 11)
  }

  // 只有当值变化时才更新
  if (pickerValue.value[0] !== yearIndex || pickerValue.value[1] !== adjustedMonthIndex) {
    pickerValue.value = [yearIndex, adjustedMonthIndex]
    emitDate(selectedYear, adjustedMonthIndex + 1) // 转换为1-based
  }
}

const emitDate = (year, month) => {
  const formattedMonth = String(month).padStart(2, '0')
  const dateStr = `${year}-${formattedMonth}`
  emit('input', dateStr)
  emit('change', dateStr)
}

// 初始化
initDefaultValue()

// 监听外部value变化
watch(
  () => props.value,
  (newVal) => {
    if (!newVal) return

    const [currentYear, currentMonth] = [yearRange[pickerValue.value[0]], pickerValue.value[1] + 1]
    const [newYear, newMonth] = newVal.split('-').map(Number)

    if (currentYear !== newYear || currentMonth !== newMonth) {
      updatePickerValue(newYear, newMonth)
    }
  },
)
</script>
<style scoped>
.month-picker-container {
  width: 100%;
  height: 150px;
  /* 改为固定像素单位更精确 */
  background: #fff;
}

.picker-view {
  height: 100%;
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 54px;
  font-size: 16px;
  font-weight: normal;
  line-height: 54px;
  color: #333;
  text-align: center;
}
</style>
