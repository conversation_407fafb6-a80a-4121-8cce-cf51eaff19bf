<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <business v-if="userRoleIsBusiness" />
  <personal v-else />
</template>

<script setup lang="ts">
import business from './module/business.vue'
import personal from './module/personal.vue'
defineOptions({
  name: 'Mine',
})

const { userRoleIsBusiness } = useUserInfo()
</script>

<style lang="scss" scoped></style>
