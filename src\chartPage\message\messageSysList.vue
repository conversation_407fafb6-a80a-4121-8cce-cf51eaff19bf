<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging :paging-style="pageStyle" v-model="pageData" @query="queryList" ref="pagingRef">
    <template #top>
      <CustomNavBar title="系统消息"></CustomNavBar>
    </template>

    <view
      class="qualiCertificate-list"
      v-for="(item, index) in pageData"
      :key="index"
      @click="goSysDetail(item.id)"
    >
      <view class="qualiCertificate-item flex-between m-b-20rpx">
        <view class="flex-c just-f">
          <view class="contanner-left-img">
            <wd-img :width="20" :height="20" :src="info1" />
          </view>
          <view class="m-t-w">
            <view class="u-line-1 p-t-10rpx font-600">{{ item.title }}</view>
            <view class="u-line-2 contanner-test text-24rpx">
              {{ item.description }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryDetailList } from '@/interPost/messege'
import info1 from '@/chartPage/img/info1.png'
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 参数
const params = ref({
  entity: {
    messageType: '',
  },
  orderBy: {},
  page: pageInfo.pageNum,
  size: pageInfo.pageSize,
})
// 去活动
const goSysDetail = (id) => {
  uni.navigateTo({
    url: `/chartPage/message/infoDetail?id=${id}&isShow=sys`,
  })
}
// 获取列表
const queryList = async () => {
  const res: any = await queryDetailList(params.value)
  if (res.code === 0) {
    pagingRef.value.complete(res.data.list)
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  pagingRef.value.reload()
  params.value.entity.messageType = options.messageType
})
</script>

<style lang="scss" scoped>
.contanner-test {
  color: rgba(85, 85, 85, 1);
}
.m-t-w {
  width: calc(100% - 100rpx);
}

.contanner-left-img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70rpx;
  height: 70rpx;
  margin-right: 20rpx;
  background: rgba(255, 142, 142, 1);
  border-radius: 50%;
}
.qualiCertificate {
  padding: 40rpx;
  .qualiCertificate-btn {
    width: 100%;
    height: 120rpx;
    font-weight: 500;
    line-height: 120rpx;
    color: #fff;
    text-align: center;
    background: rgba(83, 120, 255, 1);
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
  }
}
.qualiCertificate-list {
  padding: 20rpx 40rpx 0rpx;
  .qualiCertificate-item {
    width: 100%;
    // height: 120rpx;
    padding: 20rpx 20rpx;
    font-weight: 500;
    // line-height: 120rpx;
    background: #fff;
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
    .qualiCertificate-img {
      width: 65rpx;
      height: 48rpx;
    }
  }
}
</style>
