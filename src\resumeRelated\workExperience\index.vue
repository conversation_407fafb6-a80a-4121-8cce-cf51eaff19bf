<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="工作经历">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="pageContaner">
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">公司名称</view>
          <!-- <view class="color-8 text-32rpx" @click="goCompany">请输入</view> -->
          <view
            class="text-32rpx text-white-space"
            :class="fromData.company ? 'c-#333333' : 'c-#888888'"
            @click="goCompany"
          >
            {{ fromData.company ? fromData.company : '请填写公司名称' }}
          </view>
        </view>
        <view class="icon-right" @click="goCompany">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">所在行业</view>
          <view
            class="text-32rpx text-pre-wrap"
            :class="fromData.industry ? 'c-#333333' : 'c-#888888'"
            @click="goIndustry"
          >
            {{ fromData.industry ? fromData.industry : '请选择行业' }}
          </view>
        </view>
        <view class="icon-right" @click="goIndustry">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">在职时间</view>
          <view class="flex-c">
            <!-- <view class="color-8 text-32rpx w-40">开始时间</view> -->
            <wd-datetime-picker
              :zIndex="10001"
              type="year-month"
              v-model="fromData.startTime"
              placeholder="开始时间"
              class="flex-1"
              custom-value-class="custom-label-class"
            />
            <view class="w-20">-</view>
            <!-- <view class="color-8 text-32rpx w-30">结束时间</view> -->
            <wd-datetime-picker
              :zIndex="10001"
              type="year-month"
              v-model="fromData.endTime"
              custom-value-class="custom-label-class"
              placeholder="结束时间"
              class="flex-1"
            />
          </view>
        </view>
        <view class="icon-right">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">工作内容</view>
          <!-- <view class="color-8 text-32rpx">请输入</view> -->
          <view
            class="text-32rpx text-pre-wrap"
            :class="fromData.workDescription ? 'c-#333333' : 'c-#888888'"
            @click="goWorkDescription"
          >
            {{ fromData.workDescription ? fromData.workDescription : '工作内容' }}
          </view>
        </view>
        <view class="icon-right" @click="goWorkDescription">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <!-- <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">拥有技能</view>
          <view
            class="text-32rpx text-white-space"
            :class="fromData.skills ? 'c-#333333' : 'c-#888888'"
            @click="goSkills"
          >
            {{ fromData.skills ? fromData.skills : '非必填' }}
          </view>
        </view>
        <view class="icon-right" @click="goSkills">
          <wd-icon
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view> -->
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">工作业绩</view>
          <!-- <view class="color-8 text-32rpx">非必填</view> -->
          <view
            class="text-32rpx text-pre-wrap"
            :class="fromData.workPerformance ? 'c-#333333' : 'c-#888888'"
            @click="goWorkPerformance"
          >
            {{ fromData.workPerformance ? fromData.workPerformance : '非必填' }}
          </view>
        </view>
        <view class="icon-right">
          <wd-icon
            @click="goWorkPerformance"
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
      <view class="form-list flex-between">
        <view class="form-list-item">
          <view class="mainText">所属部门</view>
          <!-- <view class="color-8 text-32rpx">非必填</view> -->
          <view
            class="text-32rpx text-white-space"
            :class="fromData.department ? 'c-#333333' : 'c-#888888'"
            @click="goDept"
          >
            {{ fromData.department ? fromData.department : '非必填' }}
          </view>
        </view>
        <view class="icon-right">
          <wd-icon
            @click="goDept"
            name="chevron-right"
            size="20px"
            color="#888888"
            class="arrow-right-icon"
          ></wd-icon>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed flex-c">
        <view
          v-if="isAdd === 'edit'"
          class="btn-delet m-r-30rpx"
          @click="delWork"
          :class="isAdd === 'edit' ? 'w-30' : ''"
        >
          删除
        </view>
        <view class="btn_box" :class="isAdd === 'edit' ? 'w-70' : 'w-100'">
          <view class="btn_bg" @click="addWork">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>
<script setup lang="ts">
import isEqual from 'lodash/isEqual'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { getCustomBar } from '@/utils/storege'
import { useLoginStore, useResumeStore } from '@/store'
import {
  resumeWorkExperiencesDel,
  resumeWorkExperiencesAdd,
  resumeWorkExperiencesUpdate,
} from '@/interPost/resume'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// 是否是新增
const isAdd = ref('')
// 工作编辑
const objItem = ref()
// 公司
const resumeStore = useResumeStore()
// 公司id
const companyId = ref(null)
// 行业
const jobName = ref('')
const customBar = ref(0)
const expectedIndustry = ref('') // 行业
const expectedIndustryCode = ref('')
const loginStore = useLoginStore()
const id = ref(null)
// 表单
const fromData = ref({
  baseInfoId: null,
  startTime: '',
  endTime: '',
  department: '',
  company: '',
  workDescription: '',
  workPerformance: '',
  skills: '',
  industry: '',
  industryId: '',
  position: '',
  positionId: '',
  work: '',
  workId: '',
})
// 表单初始化
const fromDataInit = ref(JSON.parse(JSON.stringify(fromData.value)))

onLoad(async (options) => {
  await nextTick()
  customBar.value = getCustomBar()
  id.value = options.id

  if (resumeStore.isAdd === 'edit') {
    objItem.value = JSON.parse(decodeURIComponent(options.item))
    fromData.value = JSON.parse(decodeURIComponent(options.item))
  }
})
// 监听数据是否有变化
onShow(async () => {
  await nextTick()
  isAdd.value = resumeStore.isAdd
  companyId.value = resumeStore.companyId ? resumeStore.companyId : null
  if (resumeStore.isAdd === 'add') {
    fromData.value.department = resumeStore.department
    fromData.value.company = resumeStore.company
    fromData.value.workDescription = resumeStore.workDescription
    fromData.value.skills = resumeStore.skills
    fromData.value.workPerformance = resumeStore.workPerformance
    // fromData.value.position = loginStore.jobObjOnline.workNameF // 行业
    //   ? loginStore.jobObjOnline.workNameF
    //   : ''
    // fromData.value.positionId = loginStore.jobObjOnline.workIdF // 行业id
    //   ? loginStore.jobObjOnline.workIdF
    //   : ''
    // fromData.value.work = loginStore.jobObjOnline.workName ? loginStore.jobObjOnline.workName : '' // 岗位
    // fromData.value.workId = loginStore.jobObjOnline.workId ? loginStore.jobObjOnline.workId : '' // 岗位id
    // 岗位
    fromData.value.industry = loginStore.jobObjOnline.industry
      ? loginStore.jobObjOnline.industry
      : ''
    // 岗位type
    fromData.value.industryId = loginStore.jobObjOnline.industryId
      ? loginStore.jobObjOnline.industryId
      : ''
  } else {
    fromData.value.department = resumeStore.department
      ? resumeStore.department
      : objItem.value.department
    fromData.value.company = resumeStore.company ? resumeStore.company : objItem.value.company
    fromData.value.workDescription = resumeStore.workDescription
      ? resumeStore.workDescription
      : objItem.value.workDescription
    fromData.value.skills = resumeStore.skills ? resumeStore.skills : objItem.value.skills
    fromData.value.workPerformance = resumeStore.workPerformance
      ? resumeStore.workPerformance
      : objItem.value.workPerformance
    // fromData.value.position = loginStore.jobObjOnline.workNameF
    //   ? loginStore.jobObjOnline.workNameF
    //   : objItem.value.position
    // fromData.value.positionId = loginStore.jobObjOnline.workIdF
    //   ? loginStore.jobObjOnline.workIdF
    //   : objItem.value.positionId
    // fromData.value.work = loginStore.jobObjOnline.workName
    //   ? loginStore.jobObjOnline.workName
    //   : objItem.value.work
    // fromData.value.workId = loginStore.jobObjOnline.workId
    //   ? loginStore.jobObjOnline.workId
    //   : objItem.value.workId
    fromData.value.industry = loginStore.jobObjOnline.industry
      ? loginStore.jobObjOnline.industry
      : objItem.value.industry
    fromData.value.industryId = loginStore.jobObjOnline.industryId
      ? loginStore.jobObjOnline.industryId
      : objItem.value.industryId
    console.log(fromData.value.industry, 'fromData.value.industry')
    const obj = {
      industry: fromData.value.industry,
      industryId: Number(fromData.value.industryId),
    }
    loginStore.setjobObjOnline(obj)
  }
})
onUnload(() => {
  loginStore.setjobObjOnline({})
})
// 返回
const back = () => {
  if (resumeStore.isAdd === 'add') {
    console.log(isEqual(fromData.value, fromDataInit.value))
    if (isEqual(fromData.value, fromDataInit.value)) {
      resumeStore.setDepartment('')
      resumeStore.setCompany('')
      resumeStore.setWorkDescription('')
      resumeStore.setSkills('')
      resumeStore.setWorkPerformance('')
      resumeStore.setCompanyId('')
      loginStore.setjobObjOnline({})
      uni.navigateBack()
    } else {
      uni.showModal({
        title: '提示',
        content: '您有内容未提交保存,确认返回吗?',
        // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
        success: function (res) {
          if (res.confirm) {
            resumeStore.setDepartment('')
            resumeStore.setCompany('')
            resumeStore.setWorkDescription('')
            resumeStore.setCompanyId('')
            resumeStore.setSkills('')
            resumeStore.setWorkPerformance('')
            loginStore.setjobObjOnline({})
            uni.navigateBack()
          }
        },
      })
    }
  } else {
    console.log(objItem.value.startTime.slice(0, 7))
    if (
      objItem.value.startTime === fromData.value.startTime &&
      objItem.value.endTime === fromData.value.endTime &&
      objItem.value.department === fromData.value.department &&
      objItem.value.company === fromData.value.company &&
      objItem.value.workDescription === fromData.value.workDescription &&
      objItem.value.workPerformance === fromData.value.workPerformance &&
      objItem.value.industry === fromData.value.industry
    ) {
      resumeStore.setDepartment('')
      resumeStore.setCompany('')
      resumeStore.setWorkDescription('')
      resumeStore.setSkills('')
      resumeStore.setWorkPerformance('')
      loginStore.setjobObjOnline({})
      uni.navigateBack()
    } else {
      uni.showModal({
        title: '提示',
        content: '您有内容未提交保存,确认返回吗?',
        // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
        success: function (res) {
          if (res.confirm) {
            resumeStore.setDepartment('')
            resumeStore.setCompany('')
            resumeStore.setWorkDescription('')
            resumeStore.setSkills('')
            resumeStore.setWorkPerformance('')
            loginStore.setjobObjOnline({})
            uni.navigateBack()
          }
        },
      })
    }
  }
}
// 公司
const goCompany = () => {
  uni.navigateTo({
    url: '/resumeRelated/corporateName/index?company=' + fromData.value.company,
  })
}
// 技能
const goSkills = () => {
  uni.navigateTo({
    url: '/resumeRelated/workExperience/workSkills?skills=' + fromData.value.skills,
  })
}
// 删除
const delWork = async () => {
  uni.showModal({
    title: '提示',
    content: '您确定要删除吗?',
    // showCancel: false, // 默认显示取消按钮，这里设置为false不显示
    success: function (res) {
      if (res.confirm) {
        resumeWorkExperiencesDel({ id: objItem.value.id }).then((res: any) => {
          if (res.code === 0) {
            resumeStore.setDepartment('')
            resumeStore.setCompany('')
            resumeStore.setWorkDescription('')
            resumeStore.setSkills('')
            resumeStore.setWorkPerformance('')
            loginStore.setjobObjOnline({})
            uni.navigateBack()
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none',
              duration: 3000,
            })
          }
        })
      }
    },
  })
}
// 完成
const addWork = async () => {
  let res
  if (resumeStore.isAdd === 'add') {
    res = await resumeWorkExperiencesAdd({
      ...fromData.value,
      baseInfoId: id.value,
      companyId: companyId.value,
    })
  } else {
    res = await resumeWorkExperiencesUpdate({ ...fromData.value, companyId: companyId.value })
  }

  if (res.code === 0) {
    resumeStore.setDepartment('')
    resumeStore.setCompany('')
    resumeStore.setWorkDescription('')
    resumeStore.setSkills('')
    resumeStore.setWorkPerformance('')
    loginStore.setjobObjOnline({})
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 部门
const goDept = () => {
  uni.navigateTo({
    url: '/resumeRelated/workExperience/dept?department=' + fromData.value.department,
  })
}
// 行业
const goIndustry = () => {
  uni.navigateTo({
    url: '/resumeRelated/resumeIndustry/index',
  })
}
// 业绩
const goWorkPerformance = () => {
  uni.navigateTo({
    url:
      '/resumeRelated/workExperience/workPerformance?workPerformance=' +
      fromData.value.workPerformance,
  })
}
// 工作内容
const goWorkDescription = () => {
  uni.navigateTo({
    url:
      '/resumeRelated/workExperience/workContent?workDescription=' + fromData.value.workDescription,
  })
}
// 公司
</script>

<style lang="scss" scoped>
::v-deep .wd-input__value {
  padding: 20rpx 20rpx !important;
  background-color: #e3e3e3;
  border-radius: 20rpx;
}
::v-deep .wd-picker__cell {
  width: 100% !important;
  padding-left: 0rpx !important;
  background: transparent !important;
}
::v-deep .wd-picker__arrow {
  display: none;
}
::v-deep .custom-label-class {
  view {
    font-size: 32rpx !important;
  }
}
.w-100 {
  width: 100%;
}
::v-deep .wd-picker__placeholder {
  color: #888888 !important;
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn-delet {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 30%;
    padding: 20rpx 0rpx;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background: #959595;
    border-radius: 14px 14px 14px 14px;
  }
  .btn_box {
    box-sizing: border-box;
    // width: 70%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
.pageContaner {
  padding: 20rpx 40rpx 20rpx;

  .form-list {
    padding: 40rpx 0rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .form-list-item {
      flex: 1;
    }

    .icon-right {
      display: flex;
      justify-content: right;
      width: 100rpx;
    }
  }
}
</style>
