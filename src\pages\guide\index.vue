<route lang="json5" type="home">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-Guide">
    <view class="guide" :style="{ paddingTop: (statusBar + 200) * 2 + 'rpx' }">
      <view class="guide-box">
        <image :src="img" class="guide-box-img" />
      </view>
      <view class="guide-name">易直聘</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { setCheackInfo, setStatusBar } from '@/utils/storege'
import { newsInfo } from '@/interPost/login/index'
const { getToken } = useUserInfo()

// 获取状态栏
const statusBar = ref(0)
// Logo图
const img = ref('/static/images/login/logo.png')
// 页面加载
onLoad(() => {
  console.log('启动页面onLoad=====')
  statusBar.value = uni.getSystemInfoSync().statusBarHeight
  setStatusBar(statusBar.value)
})
// 页面更新
onShow(() => {
  if (getToken()) {
    setInit()
  } else {
    uni.reLaunch({
      url: '/pages/login/index',
    })
  }
})
const setInit = async () => {
  await uni.$onLaunched
  // 用户信息1
  const res: any = await newsInfo()
  console.log(res, '启动页最新用户信息')
  if (res.code === 0) {
    setCheackInfo(res.data)
    // 是否可以使用
    if (res.data.userStatus === 1) {
      if (res.data.userLastLoginType === 0) {
        switch (res.data.completeStep) {
          case -999:
            uni.navigateTo({
              url: '/loginSetting/category/index',
            })
            break
          case 0:
            uni.navigateTo({
              url: '/loginSetting/createResume/biographicalOne',
            })
            break
          case 1:
            uni.navigateTo({
              url: '/loginSetting/category/JobIntention',
            })
            break
          case 2:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 3:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 4:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 5:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
        }
      } else {
        switch (res.data.completeStep) {
          case -999:
            uni.navigateTo({
              url: '/loginSetting/category/index',
            })
            break
          case 0:
            uni.navigateTo({
              url: '/loginSetting/companyJoin/companyInfo',
            })
            break
          case 1:
            uni.navigateTo({
              url: '/loginSetting/companyJoin/recrIdent',
            })
            break
          case 2:
            uni.reLaunch({
              url: '/loginSetting/companyJoin/jobCertificate',
            })
            break
          case 3:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 4:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 5:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
        }
      }
    } else {
      uni.showModal({
        title: '提示',
        content: res.msg,
        showCancel: false, // 默认显示取消按钮，这里设置为false不显示
        success: function (res) {
          if (res.confirm) {
            // console.log('用户点击了确定');
          }
        },
      })
    }
  }
}
</script>

<style scoped lang="scss">
// 启动页图标，根据分辨率决定使用2倍和3倍
.bg-Guide {
  width: 100%;
  height: 100vh;
  background-color: #fff;
  /* 覆盖整个区域，保持图片比例 */
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-size: cover;
  @include graph-img('@/static/images/login/guide');

  .guide {
    .guide-box {
      width: 160rpx;
      height: 160rpx;
      margin: auto;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 40rpx;

      .guide-box-img {
        width: 120rpx;
        height: 120rpx;
        padding: 20rpx;
      }
    }

    .guide-name {
      margin-top: 20rpx;
      font-size: 40rpx;
      font-weight: 700;
      color: #000000;
      text-align: center;
    }
  }
}
</style>
