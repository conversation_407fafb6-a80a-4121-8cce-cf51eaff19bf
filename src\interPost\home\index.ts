import { POSTPaging, POST } from '@/service'
// 主页
export const getqueryList = (data: any) => {
  return POSTPaging('/easyzhipin-api/positionInfo/queryList', data)
}
// 距离
export const getmyAddress = () => {
  return POST('/easyzhipin-api/my/myAddress')
}

// 详情
export const positionInfoDetil = (data) => {
  return POST('/easyzhipin-api/positionInfo/queryByPositionId', data)
}
// 首页

export const positionInfoByjob = () => {
  return POSTPaging('/easyzhipin-api/positionInfo/queryFindJob')
}
// 地图应用key

export const queryKey = () => {
  return POST('/easyzhipin-api/map/queryKey')
}
// 首页详情hr
export const queryHrByCompanyId = (data) => {
  return POST('/easyzhipin-api/companyHr/queryHrByCompanyId', data)
}
// 首页详情公司
export const queryAllDetailId = (data) => {
  return POST('/easyzhipin-api/company/queryAllDetailId', data)
}
// 违规公示
export const queryListwg = (data) => {
  return POST('/easyzhipin-api/violationRecord/queryList', data)
}
// 我的-信息
export const myMessageShort = () => {
  return POST('/easyzhipin-api/my/myInfo')
}
// 查看个人信息(我的里面)
export const queryMyMessage = () => {
  return POST('/easyzhipin-api/my/queryMyInfo')
}
// 身份认证

export const id2MetaVerify = (data) => {
  return POST('/easyzhipin-api/user/id2MetaVerify', data)
}

// 招工作
export const queryFindJob = () => {
  return POST('/easyzhipin-api/positionInfo/queryFindJob')
}
// 取消收藏的公司(送收藏主键id)接口
export const cancelCompany = (data) => {
  return POST('/easyzhipin-api/collect/cancelCompany', data)
}
// 收藏的公司(送收藏主键id)接口
export const collectCompany = (data) => {
  return POST('/easyzhipin-api/collect/collectCompany', data)
}
// 取消收藏职位(送收藏主键id)接口
export const cancelPosition = (data) => {
  return POST('/easyzhipin-api/collect/cancelPosition', data)
}
// 收藏职位接口
export const collectPosition = (data) => {
  return POST('/easyzhipin-api/collect/collectPosition', data)
}

// 通过公司id 查看该公司下的岗位接口
export const queryPositionByCompanyId = (data) => {
  return POSTPaging('/easyzhipin-api/positionInfo/queryPositionListByCompanyId', data)
}
