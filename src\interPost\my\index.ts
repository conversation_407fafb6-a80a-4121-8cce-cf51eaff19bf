import { POST } from '@/service'
// 手机号
export const updatePhone = (data) => {
  return POST('/easyzhipin-api/resume/updatePhone', data)
}
// 微信
export const updateWxCode = (data) => {
  return POST('/easyzhipin-api/resume/updateWxCode', data)
}
// 首次工作时间
export const updateWorkTime = (data) => {
  return POST('/easyzhipin-api/resume/updateWorkTime', data)
}
// 地址
export const myAddress = (data) => {
  return POST('/easyzhipin-api/my/myAddress', data)
}
// 保存

export const saveMyAddress = (data) => {
  return POST('/easyzhipin-api/my/saveMyAddress', data)
}
// 头像上传
export const userImageAudit = (data) => {
  return POST('/easyzhipin-api/userImageAudit/add', data)
}
// 收藏公司列表
export const queryCollectCompanyList = (data) => {
  return POST('/easyzhipin-api/myDetails/queryCollectCompanyList', data)
}
// 收藏岗位列表
export const queryCollectPositionList = (data) => {
  return POST('/easyzhipin-api/myDetails/queryCollectPositionList', data)
}
