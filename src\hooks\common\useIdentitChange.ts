// 切换身份
import { changeToBAuth, changeToCAuth } from '@/interPost/common'
import { setNesInfoAllhome } from '@/hooks/common/useNewInfoAll'

export const useChangeIdent = () => {
  const { userRoleIsBusiness, setUserIntel } = useUserInfo()

  const changeIdent = async () => {
    if (!userRoleIsBusiness.value) {
      const res: any = await changeToBAuth()
      console.log(userRoleIsBusiness.value, '管理端c切b端')
      if (res.code === 0) {
        setUserIntel({ ...res.data, type: 1 })
        await setNesInfoAllhome()
      }
    } else {
      const res: any = await changeToCAuth()
      console.log(userRoleIsBusiness.value, '管理端b切c端')
      if (res.code === 0) {
        setUserIntel({ ...res.data, type: 0 })
        await setNesInfoAllhome()
      }
    }
  }

  return {
    changeIdent,
  }
}
