<template>
  <scroll-view scroll-x :show-scrollbar="false">
    <template v-if="userRoleIsBusiness"></template>
    <template v-else>
      <view class="flex items-center gap-20rpx c-#333333 text-26rpx my-30rpx">
        <view
          class="bg-white rounded-[40rpx] h-156rpx px-42rpx flex-shrink-0 flex flex-col flex-justify-center gap-30rpx"
        >
          <view class="flex items-center gap-10rpx">
            <wd-img :src="infoPosition" width="34rpx" height="34rpx" />
            <text>{{ customCardInfo.positionName }}</text>
          </view>
          <view class="flex items-center gap-10rpx">
            <wd-img :src="infoWelfare" width="34rpx" height="34rpx" />
            <text>{{ customCardInfo.positionBenefitList?.join(' · ') }}</text>
          </view>
        </view>
        <view
          class="bg-white rounded-[40rpx] h-156rpx px-42rpx flex-shrink-0 flex flex-col flex-justify-center gap-30rpx"
        >
          <view class="flex items-center gap-10rpx">
            <text>{{ customCardInfo.companyName }}</text>
          </view>
          <view class="flex items-center gap-10rpx">
            <wd-img :src="infoCompanyAddress" width="34rpx" height="34rpx" />
            <text>
              {{
                [
                  customCardInfo.provinceName,
                  customCardInfo.cityName,
                  customCardInfo.districtName,
                ].join(' · ')
              }}
            </text>
          </view>
        </view>
      </view>
    </template>
  </scroll-view>
</template>

<script lang="ts" setup>
import { positionInfoQueryIMCardInfoById } from '@/service/positionInfo'
import infoPosition from '@/ChatUIKit/static/info-position.png'
import infoCompanyAddress from '@/ChatUIKit/static/info-company-address.png'
import infoWelfare from '@/ChatUIKit/static/info-welfare.png'

interface propsInterface {
  ext: Api.IM.UserBusinessExtInfo
}

defineOptions({
  name: 'ChatInfoCard',
})

const props = withDefaults(defineProps<propsInterface>(), {})
const propsExt = computed(() => props.ext)

const { userIntel, getUserIsLogin, userRoleIsBusiness } = useUserInfo()
const { customCardInfo } = useIMConversation()

async function fetchCardInfo() {
  try {
    const hruserId = userRoleIsBusiness.value ? userIntel.value.userId : propsExt.value.hrUserId
    const userId = userRoleIsBusiness.value ? propsExt.value.hrUserId : userIntel.value.userId
    const { data } = await positionInfoQueryIMCardInfoById(
      {
        userId,
        hruserId,
      },
      {
        custom: {
          toast: true,
        },
      },
    )
    customCardInfo.value = data
  } catch (error) {
    console.error('Error fetching card info:', error)
  }
}

watch(
  [() => getUserIsLogin.value],
  ([login]) => {
    if (login) {
      fetchCardInfo()
    }
  },
  {
    immediate: true,
  },
)
</script>

<style lang="scss" scoped>
//
</style>
