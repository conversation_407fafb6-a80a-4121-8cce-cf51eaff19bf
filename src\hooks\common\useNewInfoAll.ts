import { useUserStore } from '@/store'
import { setCheackInfo } from '@/utils/storege'
import { newsInfo } from '@/interPost/login/index'
export const setNesInfoAllhome = () => {
  const { getToken } = useUserStore()
  if (getToken()) {
    setInitAllhome()
  } else {
    uni.reLaunch({
      url: '/pages/login/index',
    })
  }
}
const setInitAllhome = async () => {
  await uni.$onLaunched
  // 用户信息1
  const res: any = await newsInfo()
  console.log(res, '在onshow的newsInfo里面获取最新信息')
  if (res.code === 0) {
    setCheackInfo(res.data)
    // 是否可以使用
    if (res.data.userStatus === 1) {
      if (res.data.userLastLoginType === 0) {
        switch (res.data.completeStep) {
          case -999:
            uni.reLaunch({
              url: '/loginSetting/category/index',
            })
            break
          case 0:
            uni.reLaunch({
              url: '/loginSetting/createResume/biographicalOne',
            })
            break
          case 1:
            uni.reLaunch({
              url: '/loginSetting/category/JobIntention',
            })
            break
          case 2:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 3:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 4:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 5:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
        }
      } else {
        switch (res.data.completeStep) {
          case -999:
            uni.reLaunch({
              url: '/loginSetting/category/index',
            })
            break
          case 0:
            uni.reLaunch({
              url: '/loginSetting/companyJoin/companyInfo',
            })
            break
          case 1:
            uni.reLaunch({
              url: '/loginSetting/companyJoin/recrIdent',
            })
            break
          case 2:
            uni.reLaunch({
              url: '/loginSetting/companyJoin/jobCertificate',
            })
            break

          case 3:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 4:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
          case 5:
            uni.reLaunch({
              url: '/pages/home/<USER>',
            })
            break
        }
      }
    } else {
      uni.showModal({
        title: '提示',
        content: res.msg,
        showCancel: false, // 默认显示取消按钮，这里设置为false不显示
        success: function (res) {
          if (res.confirm) {
            console.log('确认')
          }
        },
      })
    }
  }
}
