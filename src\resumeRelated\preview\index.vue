<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img-h">
    <z-paging :refresher-enabled="false" :loading-more-enabled="false">
      <template #top>
        <CustomNavBar title="预览"></CustomNavBar>
        <view class="tabbar-list flex-c" ref="myElement">
          <view class="tabbar-item" v-for="(item, index) in tabbarlist" :key="index">
            <view
              class="tabbar-name"
              :class="index == activeIndex ? 'activeItem' : 'normalItem'"
              @click="selectItem(index)"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
      </template>

      <view class="onlineRes" v-if="activeIndex === 0">
        <view class="onlineRes-list flex-between">
          <view class="onlineRes-left">
            <view class="onlineRes-info flex-c">
              <view class="flex-c">
                <view class="name">{{ onlineObj.userName }}</view>
                <!-- <image
                  v-if="onlineObj.sex === 1"
                  class="start_ICON_item-1"
                  src="@/resumeRelated/img/<EMAIL>"
                ></image>
                <image
                  v-if="onlineObj.sex === 2"
                  class="start_ICON_item-1"
                  src="@/resumeRelated/img/sex.png"
                ></image> -->
              </view>

              <view class="onlineRes-rz flex-c" v-if="onlineObj.isApplicant === 0">
                <view class="name-rz nomal">未认证</view>
                <wd-icon name="warning" size="15px" color="#FF0000"></wd-icon>
              </view>
              <!-- <view class="onlineRes-rz flex-c" v-else>
                <view class="name-rz active">已认证</view>
                <wd-icon name="warning" size="15px" color="#333"></wd-icon>
              </view> -->
            </view>
            <!-- <view class="onlineRes-my">
              {{
                onlineObj?.workYear > 0 && onlineObj?.workYear <= 10
                  ? onlineObj.workYear + '年工作经验'
                  : onlineObj.workYear > 10
                    ? onlineObj.workYear + '年经验以上'
                    : ''
              }}·{{ onlineObj.age }}岁·本科
            </view> -->
            <view class="onlineRes-my flex items-center">
              <view v-if="onlineObj?.workYear" class="onlineRes-my-item flex items-center">
                <wd-img :width="16" :height="16" :src="icons" />
                <text class="c-#888 text-26rpx">
                  {{
                    onlineObj?.workYear > 0 && onlineObj?.workYear <= 10
                      ? onlineObj.workYear + '年'
                      : onlineObj.workYear > 10
                        ? onlineObj.workYear + '年以上'
                        : ''
                  }}
                </text>
              </view>
              <view v-if="onlineObj?.age" class="onlineRes-my-item flex items-center">
                <wd-img :width="16" :height="16" :src="birthdayIcon" />
                <text class="c-#888 text-26rpx">{{ onlineObj.age }}岁</text>
              </view>

              <!-- <text class="c-#888 text-26rpx" v-if="onlineObj.xueLi">·</text> -->
              <view v-if="onlineObj?.xueLi" class="onlineRes-my-item flex items-center">
                <wd-img :width="16" :height="16" :src="educationIcon" />
                <text class="c-#888 text-26rpx">
                  {{
                    onlineObj.xueLi === 1
                      ? '高中及以下'
                      : onlineObj.xueLi === 2
                        ? '专科'
                        : onlineObj.xueLi === 3
                          ? '本科'
                          : onlineObj.xueLi === 4
                            ? '硕士'
                            : onlineObj.xueLi === 4
                              ? '博士及以上'
                              : ''
                  }}
                </text>
              </view>
            </view>
            <view class="onlineRes-connect flex-c">
              <view class="flex-c m-right">
                <image
                  class="onlineRes-connect-img"
                  src="@/resumeRelated/img/Group_1171274957.png"
                ></image>
                <view class="onlineRes-connect-name">{{ onlineObj.telephone }}</view>
              </view>
              <view class="flex-c">
                <image
                  class="onlineRes-connect-img-1"
                  src="@/resumeRelated/img/Group_1171274958.png"
                ></image>
                <view class="onlineRes-connect-name" v-if="onlineObj.wxCode">
                  {{ onlineObj.wxCode }}
                </view>
                <view class="onlineRes-connect-name c-#4d8fff" v-else>去填写</view>
              </view>
            </view>
          </view>
          <view class="onlineRes-right text-right">
            <!-- <image src="/static/img/1.jpg"></image> -->
            <image
              v-if="onlineObj.sex === 1"
              class="border-boy"
              :src="onlineObj.headImgUrl ? onlineObj.headImgUrl : '/static/header/jobhunting1.png'"
              mode="aspectFill"
            ></image>
            <image
              v-else
              class="border-griy"
              :src="onlineObj.headImgUrl ? onlineObj.headImgUrl : '/static/header/jobhunting2.png'"
              mode="aspectFill"
            ></image>
          </view>
        </view>
        <view class="onlineRes-job flex-between">
          <view class="onlineRes-job-left flex-1">
            <view class="onlineRes-title">求职状态</view>
            <view class="p-t-20rpx p-b-20rpx text-28rpx">
              {{
                onlineObj.seekStatus === 0
                  ? '离职找工作'
                  : onlineObj.seekStatus === 1
                    ? '在职找工作'
                    : onlineObj.seekStatus === 2
                      ? '在职-看看机会'
                      : ''
              }}
            </view>
          </view>
          <view class="">
            <!-- <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon> -->
          </view>
        </view>
        <view class="work-qw">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">个人亮点</view>
            </view>
            <!-- <view class="jobExpectations-right">
              <wd-icon
                name="add-circle1"
             
                class="p-l-20rpx"
                color="#000000"
                size="18px"
              ></wd-icon>
            </view> -->
          </view>
          <view v-if="onlineObj.myLights" class="text-container p-b-10rpx p-t-10rpx">
            <text class="text-pre-wrap text-24rpx c-#666">{{ myLightsText }}</text>
            <text
              v-if="showMyLightsMore"
              class="view-detail text-24rpx"
              @click="showMyLightsDetail"
              style="color: #457ae6"
            >
              查看详情
            </text>
            <text
              v-if="showMyLightsCollapse && !showMyLightsMore"
              class="view-detail text-24rpx"
              @click="showMyLightsShrink"
              style="color: #457ae6"
            >
              收起
            </text>
          </view>
        </view>
        <view class="jobExpectations">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx p-b-10rpx">求职期望</view>
              <!-- <wd-icon name="help-circle" color="#000000" size="18px"></wd-icon> -->
            </view>
            <!-- <view class="jobExpectations-right">
              <wd-icon name="add-circle1" class="p-l-10rpx" color="#000000" size="18px"></wd-icon>
            </view> -->
          </view>
          <view v-for="(item, index) in onlineObj.jobIntentionList" :key="index">
            <view class="jobExpectations-qw text-28rpx">
              {{ item.jobType === 1 ? '全职期望' : '兼职期望' }}
            </view>
            <view class="jobExpectations-exepress flex-between">
              <view class="jobExpectations-exepress-left">
                <view class="jobExpectations-exepress-list">
                  <view class="flex-c">
                    <view class="text-name text-28rpx">{{ item.expectedPositions }}</view>
                    <view class="text-name text-salary text-28rpx">
                      {{ item.salaryExpectationStart }}-{{ item.salaryExpectationEnd }}
                    </view>
                  </view>
                  <view class="flex-c">
                    <view class="text-position">{{ item.provinceName }}</view>
                    <view class="text-position text-denery">{{ item.expectedIndustry }}</view>
                  </view>
                </view>
              </view>
              <view class="jobExpectations-exepress-right">
                <!-- <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon> -->
              </view>
            </view>
          </view>
        </view>
        <view class="education">
          <view class="jobExpectations-title flex-between m-b-30rpx">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">教育经历</view>
            </view>
            <!-- <view class="jobExpectations-right">
              <wd-icon name="add-circle1" class="p-l-20rpx" color="#000000" size="18px"></wd-icon>
            </view> -->
          </view>
          <view
            class="education-list flex-between"
            v-for="(item, index) in onlineObj.educationsList"
            :key="index"
          >
            <view class="education-left flex-c">
              <view class="education-left-img"></view>
              <view class="education-left-xl">
                <view class="text-28rpx c-#333">{{ item.school }}</view>
                <view class="education-left-xl-subname text-28rpx c-#888">{{ item.major }}</view>
              </view>
            </view>
            <view class="education-right flex-between">
              <view></view>
              <view class="flex-c">
                <view class="time">
                  {{ item.startTime.substring(0, 4) }}-{{ item.endTime.substring(0, 4) }}
                </view>
                <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon>
              </view>
            </view>
          </view>
        </view>
        <view class="work-qw">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">工作经历</view>
            </view>
            <!-- <view class="jobExpectations-right">
              <wd-icon name="add-circle1" class="p-l-20rpx" color="#000000" size="18px"></wd-icon>
            </view> -->
          </view>
          <view>
            <view
              class="work-qw-content"
              v-for="(item, index) in onlineObj.workExperiencesList"
              :key="index"
            >
              <view class="work-qw-title flex-between">
                <view class="flex-c">
                  <view class="text-28rpx c-#000">{{ item.company }}</view>
                  <!-- <view class="m-l-20rpx">{{}}-13000</view> -->
                </view>
                <!-- <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon> -->
              </view>
              <view class="flex-between work-qw-line">
                <view class="c-#888 text-24rpx">{{ item.industry }}</view>
                <view class="c-#888 text-22rpx">
                  {{ item.startTime.slice(0, 7) }}至{{ item.endTime.slice(0, 7) }}
                </view>
              </view>
              <view class="p-b-10rpx p-t-10rpx">
                <view class="text-container">
                  <text class="text-pre-wrap c-#888 text-24rpx">
                    内容：{{ getWorkDescriptionText(item, index) }}
                  </text>
                  <text
                    v-if="shouldShowWorkMore(item, index)"
                    class="view-detail text-24rpx"
                    @click="showWorkDetail(index)"
                    style="color: #457ae6"
                  >
                    查看详情
                  </text>
                  <text
                    v-if="shouldShowWorkCollapse(item, index)"
                    class="view-detail text-24rpx"
                    @click="showWorkShrink(index)"
                    style="color: #457ae6"
                  >
                    收起
                  </text>
                </view>
              </view>
              <!-- <view class="wx-tag">文案編輯</view> -->
            </view>
          </view>
        </view>
        <view class="work-qw">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">项目经历</view>
            </view>
            <!-- <view class="jobExpectations-right">
              <wd-icon name="add-circle1" class="p-l-20rpx" color="#000000" size="18px"></wd-icon>
            </view> -->
          </view>

          <view class="work-qw-content" v-for="(item, index) in onlineObj.projectList" :key="index">
            <view class="work-qw-title flex-between">
              <view class="flex-c">
                <view class="text-28rpx c-#000">{{ item.projectName }}</view>
              </view>
              <wd-icon name="chevron-right" color="#333333" size="18px"></wd-icon>
            </view>
            <view class="flex-between work-qw-line">
              <view class="c-#888 text-24rpx">{{ item.takeOffice }}</view>
              <view class="c-#888 text-22rpx">
                {{ item.startDate.slice(0, 7) }}-{{ item.endDate.slice(0, 7) }}
              </view>
            </view>
            <view class="p-b-10rpx p-t-10rpx">
              <view class="text-container">
                <text class="text-pre-wrap c-#888 text-24rpx">
                  内容：{{ getProjectDescText(item, index) }}
                </text>
                <text
                  v-if="shouldShowProjectMore(item, index)"
                  class="view-detail text-24rpx"
                  @click="showProjectDetail(index)"
                  style="color: #457ae6"
                >
                  查看详情
                </text>
                <text
                  v-if="shouldShowProjectCollapse(item, index)"
                  class="view-detail text-24rpx"
                  @click="showProjectShrink(index)"
                  style="color: #457ae6"
                >
                  收起
                </text>
              </view>
            </view>
          </view>
        </view>
        <view class="qualification">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">资格证书</view>
              <!-- <wd-icon name="help-circle" color="#000000" size="18px"></wd-icon> -->
            </view>
            <!-- <view class="jobExpectations-right">
              <wd-icon name="add-circle1" class="p-l-10rpx" color="#000000" size="18px"></wd-icon>
            </view> -->
          </view>
          <view class="qualification-list flex-c">
            <view
              class="qualification-tag"
              v-for="(item, index) in onlineObj.resumeCertificateVOList"
              :key="index"
            >
              <!-- <image :src="item.pic" mode=""></image> -->
              <view class="qualification-tag-name">
                {{ item.certificate }}
              </view>
            </view>
          </view>
        </view>
        <view class="qualification">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">掌握技能</view>
              <!-- <wd-icon name="help-circle" color="#000000" size="18px"></wd-icon> -->
            </view>
            <!-- <view class="jobExpectations-right">
              <wd-icon name="add-circle1" color="#000000" size="18px"></wd-icon>
            </view> -->
          </view>

          <view class="qualification-list flex-c" v-if="onlineObj.skills">
            <view class="qualification-tag" v-for="(item, index) in onlineObj.skills" :key="index">
              <view class="qualification-tag-name">
                {{ item }}
              </view>
            </view>
          </view>
        </view>
        <view class="Portfolio">
          <view class="jobExpectations-title flex-between">
            <view class="jobExpectations-left flex-c">
              <view class="onlineRes-title m-r-10rpx">作品集上传</view>
            </view>
            <!-- <view class="jobExpectations-right">
              <wd-icon name="add-circle1" color="#000000" size="18px"></wd-icon>
            </view> -->
          </view>
          <view class="Portfolio-subtitle onlineRes-subtitle">不得大于20MB，图片格式</view>
          <view class="Portfolio-input">
            <wd-input readonly v-model="url" no-border placeholder="作品集链接" />
          </view>
          <!-- <view class="Portfolio-upload">
            <image src="/static/img/zuimeizuopinji_1.png" class="Portfolio-upload-img"></image>
            <view class="Portfolio-text onlineRes-subtitle p-t-10rpx">
              上传作品集上传「不得大于20MB」
            </view>
          </view> -->
        </view>
      </view>
      <view class="onlineRes" v-if="activeIndex === 1">
        <view class="my-jl-card">
          <view class="flex-between">
            <view class="my-jl-card-left flex-c">
              <view>
                <image
                  v-if="cardList?.sex === 1"
                  :src="
                    cardList.headImgUrl ? cardList.headImgUrl : '/static/header/jobhunting1.png'
                  "
                  class="my-jl-card-left-header border-boy"
                ></image>
                <image
                  v-else
                  :src="
                    cardList.headImgUrl ? cardList.headImgUrl : '/static/header/jobhunting2.png'
                  "
                  class="my-jl-card-left-header border-griy"
                ></image>
              </view>

              <view class="my-jl-card-left-item">
                <view class="card-name p-b-10rpx">
                  {{ cardList.trueName }}·{{ cardList.expectedPositions }}
                </view>
                <view class="card-name-subname subText">
                  期望薪资·{{
                    handleSalaryDisplay(
                      cardList.salaryExpectationStart,
                      cardList.salaryExpectationEnd,
                    )
                  }}
                </view>
              </view>
            </view>
            <view class="my-jl-card-right flex-c p-t-30rpx">
              <view
                class="my-jl-card-right-btn flex-c"
                :class="index === 2 ? 'activeColor' : ''"
                v-for="(item, index) in list1"
                :key="index"
              >
                <image v-if="item.img" :src="item.img" class="pic"></image>
                <view class="subText m-l-10rpx" :class="index == 2 ? 'activeText' : ''">
                  {{ item.name }}
                </view>
              </view>
            </view>
          </view>
          <view class="my-jl-card-cunstrct flex-c">
            <image
              src="@/resumeRelated/img/gongwenbao-2_1.png"
              class="my-jl-card-cunstrct-img"
            ></image>
            <view class="my-jl-card-cunstrct-name subText">·{{ cardList.expectedPositions }}</view>
          </view>
          <view class="my-jl-card-content subText u-line-2">
            {{ cardList.myLights }}
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryFullData, myCard } from '@/interPost/resume'
import { numberTokw } from '@/utils/common'
import icons from '@/resumeRelated/img/icons.png'
import birthdayIcon from '@/resumeRelated/img/birthday_icon.png'
import educationIcon from '@/resumeRelated/img/education_icon.png'

const activeIndex = ref(0)
const value = ref('')
const myElement = ref(null)
const onlineObj = ref<AnyObject>({})
const url = ref('')
const cardList = ref({})
const { getDictLabel } = useDictionary()

// 个人优势文本处理相关变量
const myLightsText = ref('') // 显示的个人优势文本
const myLightsOriginal = ref('') // 原始个人优势文本
const showMyLightsMore = ref(false) // 是否显示"查看详情"按钮
const showMyLightsCollapse = ref(false) // 是否显示"收起"按钮
const myLightsExpanded = ref(false) // 是否已展开

// 工作经历文本处理相关变量
const workExpandedStates = ref<Record<number, boolean>>({}) // 工作经历展开状态

// 项目经历文本处理相关变量
const projectExpandedStates = ref<Record<number, boolean>>({}) // 项目经历展开状态

const list1 = ref([])

const tabbarlist = ref([
  {
    name: '在线简历',
  },
  {
    name: '首页卡片',
  },
])

onLoad(() => {
  getList()
  myCardList()
})
// 获取简历信息
const getList = async () => {
  await uni.$onLaunched
  const res: any = await queryFullData()
  if (res.code === 0) {
    if (
      Array.isArray(res.data.skills) &&
      res.data.skills.length === 1 &&
      res.data.skills[0] === ''
    ) {
      res.data.skills = []
    }
    url.value = res.data.resumeFileVOList.length > 0 ? res.data.resumeFileVOList[0].url : ''
    res.data.jobIntentionList.forEach((ele: any) => {
      ele.salaryExpectationStart =
        ele.salaryExpectationStart === 0 ? '面议' : numberTokw(ele.salaryExpectationStart + '')
      ele.salaryExpectationEnd =
        ele.salaryExpectationEnd === 0 ? '面议' : numberTokw(ele.salaryExpectationEnd + '')
      if (ele.distanceMeters)
        ele.distanceMeters = Math.floor(parseInt(ele.distanceMeters) / 1000) + 'km'
    })
    onlineObj.value = res.data

    // 处理个人优势文本
    if (res.data.myLights) {
      myLightsOriginal.value = res.data.myLights
      checkMyLightsTextLength()
    }

    // 初始化工作经历展开状态
    if (res.data.workExperiencesList) {
      workExpandedStates.value = {}
      res.data.workExperiencesList.forEach((_, index) => {
        workExpandedStates.value[index] = false
      })
    }

    // 初始化项目经历展开状态
    if (res.data.projectList) {
      projectExpandedStates.value = {}
      res.data.projectList.forEach((_, index) => {
        projectExpandedStates.value[index] = false
      })
    }
  }
}
// 卡片
const myCardList = async () => {
  const res: any = await myCard()
  if (res.code === 0) {
    cardList.value = res.data
    cardList.value.salaryExpectationStart =
      res.data.salaryExpectationStart === 0
        ? '面议'
        : numberTokw(res.data.salaryExpectationStart + '')
    cardList.value.salaryExpectationEnd =
      res.data.salaryExpectationEnd === 0 ? '面议' : numberTokw(res.data.salaryExpectationEnd + '')
    if (res.data.qualification) {
      const qualificationName = await getDictLabel(105, res.data.qualification)
      list1.value.push({ name: qualificationName })
    }
    if (res.data.workAge && res.data.workAge > 0) {
      const name = res.data.workAge > 10 ? '10年以上' : `${res.data.workAge}年`
      list1.value.push({ name })
    }
    if (res.data.certificate && res.data.certificate.trim() !== '') {
      list1.value.push({ name: `${res.data.certificate}` })
    }
  }
}

// 检查个人优势文本长度并设置显示状态
const checkMyLightsTextLength = () => {
  if (!myLightsOriginal.value) {
    myLightsText.value = ''
    showMyLightsMore.value = false
    showMyLightsCollapse.value = false
    return
  }

  // 如果原始文本长度大于95字符
  if (myLightsOriginal.value.length > 75) {
    if (!myLightsExpanded.value) {
      // 默认显示简洁内容（前95个字符 + ...）
      myLightsText.value = myLightsOriginal.value.substring(0, 75) + '...'
      showMyLightsMore.value = true // 显示"查看详情"按钮
      showMyLightsCollapse.value = false // 隐藏"收起"按钮
    } else {
      // 显示完整内容
      myLightsText.value = myLightsOriginal.value
      showMyLightsMore.value = false // 隐藏"查看详情"按钮
      showMyLightsCollapse.value = true // 显示"收起"按钮
    }
  } else {
    // 文本长度不超过95字符，直接显示全部内容，不显示任何按钮
    myLightsText.value = myLightsOriginal.value
    showMyLightsMore.value = false
    showMyLightsCollapse.value = false
  }
}

// 显示个人优势详情
const showMyLightsDetail = () => {
  if (showMyLightsMore.value) {
    myLightsExpanded.value = true
    checkMyLightsTextLength()
  }
}

// 收起个人优势
const showMyLightsShrink = () => {
  if (showMyLightsCollapse.value) {
    myLightsExpanded.value = false
    checkMyLightsTextLength()
  }
}

// 工作经历相关方法
const getWorkDescriptionText = (item: any, index: number) => {
  if (!item.workDescription) return ''

  const isExpanded = workExpandedStates.value[index] || false
  if (item.workDescription.length > 75 && !isExpanded) {
    return item.workDescription.substring(0, 75) + '...'
  }
  return item.workDescription
}

const shouldShowWorkMore = (item: any, index: number) => {
  if (!item.workDescription || item.workDescription.length <= 75) return false
  return !workExpandedStates.value[index]
}

const shouldShowWorkCollapse = (item: any, index: number) => {
  if (!item.workDescription || item.workDescription.length <= 75) return false
  return workExpandedStates.value[index]
}

const showWorkDetail = (index: number) => {
  workExpandedStates.value[index] = true
}

const showWorkShrink = (index: number) => {
  workExpandedStates.value[index] = false
}

// 项目经历相关方法
const getProjectDescText = (item: any, index: number) => {
  if (!item.projectDescs) return ''

  const isExpanded = projectExpandedStates.value[index] || false
  if (item.projectDescs.length > 75 && !isExpanded) {
    return item.projectDescs.substring(0, 75) + '...'
  }
  return item.projectDescs
}

const shouldShowProjectMore = (item: any, index: number) => {
  if (!item.projectDescs || item.projectDescs.length <= 75) return false
  return !projectExpandedStates.value[index]
}

const shouldShowProjectCollapse = (item: any, index: number) => {
  if (!item.projectDescs || item.projectDescs.length <= 75) return false
  return projectExpandedStates.value[index]
}

const showProjectDetail = (index: number) => {
  projectExpandedStates.value[index] = true
}

const showProjectShrink = (index: number) => {
  projectExpandedStates.value[index] = false
}

// 处理薪资显示
const handleSalaryDisplay = (workSalaryBegin: any, workSalaryEnd: any) => {
  const isBeginNegotiable = workSalaryBegin === '面议'
  const isEndNegotiable = workSalaryEnd === '面议'
  if (isBeginNegotiable && isEndNegotiable) {
    return '面议'
  }
  if (!workSalaryBegin && !workSalaryEnd) {
    return ''
  }

  if (isBeginNegotiable || isEndNegotiable) {
    return isBeginNegotiable ? workSalaryEnd || '面议' : workSalaryBegin || '面议'
  }

  if (workSalaryBegin && !workSalaryEnd) return `${workSalaryBegin}`
  if (!workSalaryBegin && workSalaryEnd) return `${workSalaryEnd}`

  return `${workSalaryBegin}-${workSalaryEnd}`
}
const selectItem = (index) => {
  activeIndex.value = index
  console.log(activeIndex.value, 'activeIndex===')
}

onLoad(() => {})
</script>

<style scoped lang="scss">
/* Styles remain exactly the same */
::v-deep .wd-picker__value {
  font-size: 28rpx;
  color: #333;
}
::v-deep .wd-input__value {
  padding: 20rpx 20rpx !important;
  background-color: #e3e3e3;
  border-radius: 20rpx;
}
::v-deep .wd-input {
  background-color: transparent !important;
}
::v-deep .wd-picker__cell {
  width: 100% !important;
  padding-left: 0rpx !important;
  background: transparent !important;
}
::v-deep .wd-picker__arrow {
  display: none;
}
.border-boy {
  border: 3rpx solid #3e9cff;
}
.border-griy {
  border: 3rpx solid rgba(255, 190, 190, 1);
}
.activeColor {
  background-color: #f3f3f3 !important;
}
.start_ICON_item-1 {
  width: 35rpx;
  height: 35rpx;
}
.activeText {
  color: #888888 !important;
}
.active {
  color: #000 !important;
}
.nomal {
  color: #ff0000 !important;
}
.my-jl-card {
  padding: 20rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

  .my-jl-card-left {
    justify-content: flex-start;
    width: 50%;

    .my-jl-card-left-header {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
    }

    .my-jl-card-left-item {
      margin-left: 10rpx;

      .card-name {
        font-size: 28rpx;
        font-weight: 500;
        color: #555;
      }
    }
  }

  .my-jl-card-right {
    flex: 1;
    flex-wrap: wrap;
    justify-content: right;

    .my-jl-card-right-btn {
      padding: 2rpx 30rpx;
      margin-bottom: 15rpx;
      margin-left: 10rpx;
      background-color: #f3f3f3;
      border-radius: 10rpx;

      .pic {
        width: 20rpx;
        height: 20rpx;
      }
    }
  }

  .my-jl-card-cunstrct {
    line-height: 52rpx;

    .my-jl-card-cunstrct-img {
      width: 28rpx;
      height: 28rpx;
    }
  }
}

.tabbar-list {
  justify-content: center;
  padding: 0rpx 40rpx 0rpx;

  .tabbar-item {
    width: 200rpx;
    padding: 20rpx;
    text-align: center;
  }

  .normalItem {
    color: #aaaaaa;
    border-bottom: 0rpx solid transparent;
  }

  .activeItem {
    position: relative;
    font-weight: 500;
    color: #333;
  }

  .activeItem::after {
    position: absolute;
    bottom: -10rpx;
    left: 0;
    width: 160rpx;
    height: 8rpx;
    content: '';
    background-image: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
    border-radius: 20rpx;
  }
}

.onlineRes-rz {
  margin-left: 30rpx;
}

.name-rz {
  margin-right: 4rpx;
  font-size: 26rpx;
  color: #ff0000;
}

.onlineRes-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.onlineRes-subtitle {
  font-size: 24rpx;
  color: #666666;
}

.work-qw-line {
  line-height: 50rpx;
}

.onlineRes {
  padding: 40rpx;

  .onlineRes-list {
    padding-bottom: 30rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .onlineRes-left {
      width: 70%;

      .onlineRes-info {
        .name {
          font-size: 32rpx;
          font-weight: bold;
          color: #000000;
        }

        .onlineRes-rz {
          margin-left: 50rpx;

          .name-rz {
            margin-right: 4rpx;
            font-size: 26rpx;
            color: #ff0000;
          }
        }
      }

      .onlineRes-my {
        font-size: 26rpx;
        line-height: 50rpx;
        color: #888888;

        .onlineRes-my-item {
          margin-right: 20rpx;
          display: flex;
          align-items: center;
          gap: 8rpx;

          // 确保图标和文本垂直居中对齐
          :deep(wd-img) {
            display: flex;
            align-items: center;
            flex-shrink: 0; // 防止图标被压缩
          }

          text {
            line-height: 1.2; // 设置合适的行高
            display: flex;
            align-items: center;
          }
        }
      }

      .onlineRes-connect {
        .m-right {
          margin-right: 50rpx;
        }

        .onlineRes-connect-img {
          width: 24rpx;
          height: 28rpx !important;
        }

        .onlineRes-connect-img-1 {
          width: 30rpx;
          height: 26rpx;
        }

        .onlineRes-connect-name {
          padding-left: 5rpx;
          font-size: 26rpx;
          color: #888888;
        }
      }
    }

    .onlineRes-right {
      width: 30%;

      image {
        width: 120rpx;
        height: 120rpx;
        margin: 0 0 0 auto;
        border-radius: 50%;
      }
    }
  }

  .onlineRes-job {
    padding: 30rpx 0 15rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .onlineRes-job-left {
      .onlineRes-time {
        padding-top: 10rpx;
        font-size: 28rpx;
        color: #333333;
      }
    }
  }

  .jobExpectations {
    padding: 30rpx 0rpx 20rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .jobExpectations-qw {
      line-height: 60rpx;
    }

    .jobExpectations-exepress {
      padding-bottom: 20rpx;

      .jobExpectations-exepress-left {
        .text-name {
          line-height: 60rpx;
        }

        .text-salary {
          padding-left: 20rpx;
        }

        .text-position {
          font-size: 24rpx;
          color: #888888;
        }

        .text-denery {
          padding-left: 40rpx;
        }
      }
    }
  }

  .work-qw {
    padding-top: 50rpx;

    .jobExpectations-exepress {
      .jobExpectations-exepress-left {
        .text-name {
          line-height: 60rpx;
        }

        .text-salary {
          padding-left: 20rpx;
        }

        .text-position {
          font-size: 24rpx;
          color: #888888;
        }

        .text-denery {
          padding-left: 40rpx;
        }
      }
    }

    .work-qw-title {
      line-height: 60rpx;
    }

    .work-qw-content {
      padding: 10rpx 0rpx 30rpx;
      border-bottom: 1rpx solid #d7d6d6;
    }

    .wx-tag {
      width: 160rpx;
      padding: 2rpx 5rpx;
      font-size: 24rpx;
      color: #888888;
      text-align: center;
      background-color: #e5e5e5;
      border-radius: 10rpx;
    }
  }

  .education {
    padding-top: 30rpx;
    padding-bottom: 10rpx;
    border-bottom: 1rpx solid #d7d6d6;

    .education-list {
      padding: 0rpx 0rpx 30rpx;

      .education-left {
        width: 70%;

        .education-left-img {
          width: 80rpx;
          height: 80rpx;
          margin-right: 20rpx;
          background-image: url('/static/img/Group_1171274967.png');
          background-position: 100% 100%;
          background-size: 100% 100%;
        }

        .education-left-xl {
          .education-left-xl-subname {
            color: #888888;
          }
        }
      }

      .education-right {
        width: 30%;

        .time {
          font-size: 22rpx;
          color: #888888;
        }
      }
    }
  }

  .qualification {
    padding: 50rpx 0 17rpx 0;
    border-bottom: 1rpx solid #d7d6d6;

    .qualification-list {
      flex-wrap: wrap !important;
      width: 100%;
      padding: 10rpx 0rpx 30rpx;

      .qualification-tag {
        // width: 33%;
        padding-top: 20rpx;
        text-align: center;

        .qualification-tag-name {
          padding: 5rpx 20rpx;
          margin-right: 10rpx;
          font-size: 22rpx;
          color: #888888;
          background-color: #d9d9d9;
          border-radius: 10rpx;
        }
      }
    }
  }

  .Portfolio {
    padding: 30rpx 0rpx 160rpx;

    .Portfolio-subtitle {
      padding-bottom: 40rpx;
    }

    .Portfolio-input {
      padding-bottom: 40rpx;
    }

    .Portfolio-upload {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .Portfolio-upload-img {
        width: 120rpx;
        height: 120rpx;
      }
    }
  }

  .text-container {
    .view-detail {
      color: #457ae6;
      cursor: pointer;
      margin-left: 10rpx;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
</style>
