<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="containner bg-img">
    <CustomNavBar title="期望地区"></CustomNavBar>
    <view class="content_flex">
      <view class="content_search">
        <view class="content_search_bg">
          <view class="content_search_left">
            <image src="/static/img/sousuo-2_1 (3).png" mode="aspectFill"></image>
          </view>
          <view class="content_search_right">
            <wd-input no-border placeholder="搜索您想要的内容" v-model="keyword"></wd-input>
          </view>
        </view>
      </view>
    </view>

    <!-- 主列表容器 -->
    <view class="page-list">
      <!-- 左侧父级分类 -->
      <scroll-view
        scroll-y
        :style="{ height: `calc(100vh - ${scrollViewHeight}px)` }"
        class="page-list-left"
      >
        <view v-for="(parent, pIndex) in cityList" :key="pIndex">
          <view
            :class="[
              activeFIndex === pIndex ? 'activeBg' : 'normalBg',
              pIndex === activeFIndex - 1 ? 'prev-selected' : '',
              pIndex === activeFIndex + 1 ? 'next-selected' : '',
              'page-list-left-text',
            ]"
            class="page-list-left-text"
            @click="activeF(pIndex)"
          >
            {{ parent.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 右侧子级及孙子级内容 -->
      <scroll-view
        scroll-y
        :style="{ height: `calc(100vh - ${scrollViewHeight}px)` }"
        class="page-list-right"
      >
        <view v-for="(child, cIndex) in currentChildren" :key="cIndex">
          <view class="page-list-right-p">
            <view
              class="page-list-right-title"
              :class="child.active ? 'myStyle-box' : ''"
              @click="changeChildActive(cIndex)"
            >
              全{{ child.name }}
            </view>
            <view class="page-tag-list">
              <view
                v-for="(grandchild, gIndex) in child.childerCityList"
                :key="gIndex"
                class="tag-select-r"
                :class="grandchild.active ? 'myStyle-box' : ''"
                @click="changeActive(cIndex, gIndex)"
              >
                {{ grandchild.name }}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import cityData from '@/utils/json/city.json'
import { getCustomBar } from '@/utils/storege'
import { useLoginStore } from '@/store'
// vuex数据
const loginStore = useLoginStore()
const keyword = ref('')
const activeFIndex = ref(0)
const cityList = ref([])
const scrollViewHeight = ref(0)

const currentChildren = computed(() => {
  return cityList.value[activeFIndex.value]?.childerCityList || []
})

const activeF = (index) => {
  activeFIndex.value = index
}

const resetAllActive = () => {
  cityList.value.forEach((parent) => {
    parent.childerCityList &&
      parent.childerCityList.forEach((child) => {
        child.active = false
        child.childerCityList &&
          child.childerCityList.forEach((grandchild: AnyObject) => {
            grandchild.active = false
          })
      })
  })
}

const changeChildActive = (cIndex) => {
  resetAllActive()
  const target = cityList.value[activeFIndex.value].childerCityList[cIndex]
  target.active = !target.active
  const city = {
    provinceName: cityList.value[activeFIndex.value].name,
    provinceCode: cityList.value[activeFIndex.value].code,
    cityCode: cityList.value[activeFIndex.value].childerCityList[cIndex].code,
    cityName: cityList.value[activeFIndex.value].childerCityList[cIndex].name,
    districtCode: '',
    districtName: '',
  }
  if (loginStore.homeJobAvtive === 0) {
    loginStore.sethomeCity1(city)
  } else if (loginStore.homeJobAvtive === 1) {
    loginStore.sethomeCity2(city)
  } else {
    loginStore.sethomeCity3(city)
  }
  uni.navigateBack({
    delta: 1,
    success() {
      uni.$emit('refresh-a-page') // 触发刷新事件
    },
  })
}

const changeActive = (cIndex, gIndex) => {
  resetAllActive()
  const target = cityList.value[activeFIndex.value].childerCityList[cIndex].childerCityList[gIndex]
  target.active = !target.active
  const city = {
    provinceName: cityList.value[activeFIndex.value].name,
    provinceCode: cityList.value[activeFIndex.value].code,
    cityCode: cityList.value[activeFIndex.value].childerCityList[cIndex].code,
    cityName: cityList.value[activeFIndex.value].childerCityList[cIndex].name,
    districtCode:
      cityList.value[activeFIndex.value].childerCityList[cIndex].childerCityList[gIndex].code,
    districtName:
      cityList.value[activeFIndex.value].childerCityList[cIndex].childerCityList[gIndex].name,
  }
  console.log(city.provinceName, city.provinceCode, '省')
  console.log(city.districtName, city.districtCode, '区')
  if (loginStore.homeJobAvtive === 0) {
    loginStore.sethomeCity1(city)
  } else if (loginStore.homeJobAvtive === 1) {
    loginStore.sethomeCity2(city)
  } else {
    loginStore.sethomeCity3(city)
  }
  uni.navigateBack({
    delta: 1,
    success() {
      uni.$emit('refresh-a-page') // 触发刷新事件
    },
  })
}

// 根据选中的城市信息设置选中状态
const setSelectedCity = (city) => {
  if (!city || !city.provinceCode) return

  // 找到对应的省份索引
  const provinceIndex = cityList.value.findIndex((item) => item.code === city.provinceCode)
  if (provinceIndex === -1) return

  // 设置当前激活的省份
  activeFIndex.value = provinceIndex

  // 找到对应的城市
  const province = cityList.value[provinceIndex]
  if (!province.childerCityList) return

  const cityIndex = province.childerCityList.findIndex((item) => item.code === city.cityCode)
  if (cityIndex === -1) return

  // 如果有区县信息，则设置区县选中状态
  if (city.districtCode) {
    const cityItem = province.childerCityList[cityIndex]
    if (cityItem.childerCityList) {
      const districtIndex = cityItem.childerCityList.findIndex(
        (item) => item.code === city.districtCode,
      )
      if (districtIndex !== -1) {
        resetAllActive()
        cityItem.childerCityList[districtIndex].active = true
        return
      }
    }
  }

  // 如果没有区县信息或没找到，则设置城市选中状态
  resetAllActive()
  province.childerCityList[cityIndex].active = true
}

onLoad(() => {
  scrollViewHeight.value = getCustomBar() + 75
  const list = cityData.cityList.cityListData
  list.forEach((parent) => {
    parent.childerCityList &&
      parent.childerCityList.forEach((child) => {
        child.active = false
        child.childerCityList &&
          child.childerCityList.forEach((grandchild: AnyObject) => {
            grandchild.active = false
          })
      })
  })
  cityList.value = list

  // 根据当前选择的城市设置选中状态
  let selectedCity = null
  if (loginStore.homeJobAvtive === 0) {
    selectedCity = loginStore.homeCity1
  } else if (loginStore.homeJobAvtive === 1) {
    selectedCity = loginStore.homeCity2
  } else {
    selectedCity = loginStore.homeCity3
  }

  if (selectedCity) {
    setSelectedCity(selectedCity)
  }
})

onShow(() => {
  // Show logic if needed
})
</script>

<style lang="scss" scoped>
::v-deep .wd-input {
  width: 100%;
  background-color: transparent !important;
}
::v-deep .wd-input__placeholder {
  font-size: 28rpx !important;
  color: #fff !important;
}
::v-deep .wd-input__inner {
  font-size: 28rpx !important;
  font-weight: 500;
  color: #fff !important;
}

.containner-select-list {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 0 !important;
}

.myStyle-box {
  position: relative;
  border: 1px solid #1160ff;
}
.myStyle-box::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('@/loginSetting/img/Mask_group(2).png');
}

.page-list-right-title {
  position: relative;
  width: 200rpx;
  padding: 8rpx 0;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #555;
  text-align: center;
  background-color: #fff;
  border-radius: 10rpx;
}

.page-list-right-p {
  padding: 30rpx 20rpx 0rpx !important;
}

.activeBg {
  font-weight: 600;
  color: #1160ff;
  background: transparent;
}

.tag-select {
  position: relative;
  width: 200rpx;
  padding: 8rpx 0;
  color: #555;
  text-align: center;
  background-color: #fff;
  border: 1px solid #1160ff;
  border-radius: 10rpx;
}

.normalBg {
  background: #e8e8e8;
}

.u-searchs {
  padding: 0 46rpx;
  border-bottom: 1rpx solid $uni-border-b-color;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 40rpx 40rpx;
  padding-bottom: 20rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 30rpx;
      color: #fff;
      background: rgba(61, 61, 61, 0.34);
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 10%;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.page-list {
  display: flex;
  padding: 0;

  &-left {
    width: 220rpx;

    &-text {
      padding: 30rpx 10rpx 30rpx 10rpx;
      font-size: 28rpx;
      text-align: center;
      transition: all 0.3s;

      &.prev-selected {
        border-bottom-right-radius: 20rpx;
      }

      &.next-selected {
        border-top-right-radius: 20rpx;
      }
    }
  }

  &-right {
    flex: 1;

    &-p {
      padding: 40rpx 30rpx;

      &-title {
        margin-bottom: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }
}

.page-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
  justify-content: space-between;

  .tag-select-r {
    position: relative;
    width: 225rpx;
    padding: 12rpx 4rpx !important;
    margin-bottom: 10rpx;
    font-size: 28rpx;
    color: #333;
    text-align: center;
    background: #f5f5f5;
    border-radius: 10rpx;
    transition: all 0.3s;

    &.myStyle-box {
      border: 1px solid #1160ff;
    }
  }
}
</style>
