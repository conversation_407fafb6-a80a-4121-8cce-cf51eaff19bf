<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="accountLogin bg-img">
    <CustomNavBar>
      <template #right>
        <wd-img :src="identitySwitchingImg" width="45rpx" height="45rpx" @click="changeIdentFun" />
      </template>
    </CustomNavBar>
    <view class="containner-page">
      <view class="containner-title">公司信息</view>
      <view class="containner-group">
        <wd-upload
          reupload
          v-model:file-list="fileList"
          image-mode="aspectFill"
          :limit="1"
          :header="header"
          :action="baseUrl"
          @success="successFun"
          custom-class="custom-class"
          accept="image"
          :max-size="10 * 1024 * 1024"
        >
          <image
            class="containner-group-img"
            src="@/loginSetting/img/<EMAIL>"
          ></image>
          <view class="flex-c containner-group-text">
            <wd-icon name="photo" size="20px" color="#fff"></wd-icon>
            <view class="text-24rpx white-color m-l-10rpx">上传/拍摄</view>
          </view>
        </wd-upload>
      </view>
      <view class="containner-card">
        <view class="flex-c p-b-10rpx">
          <view class="text-24rpx">公司全称：</view>
          <wd-input
            class="flex-1"
            type="text"
            no-border
            v-model="fromData.companyName"
            placeholder="公司全称"
          />
        </view>
        <view class="flex-c p-b-10rpx">
          <view class="text-24rpx">公司法人：</view>
          <wd-input
            class="flex-1"
            type="text"
            no-border
            v-model="fromData.legalPerson"
            placeholder="公司法人"
          />
        </view>
        <view class="flex-c">
          <view class="text-24rpx">统一社会信用代码：</view>
          <wd-input
            type="text"
            class="flex-1"
            no-border
            v-model="fromData.creditCode"
            placeholder="统一社会信用代码"
          />
        </view>
      </view>
      <view class="containner-subText">
        <view class="containner-subText-name">注意事项:</view>
        <view class="containner-subText-name">1.上传/拍摄与所填公司全称一致的最新营业执照</view>
        <view class="containner-subText-name">2.营业执照完整,并执照信息、公章清晰可辨</view>
        <view class="containner-subText-name">3.如上传营业执照复印件/彩印件，请加盖公司公章</view>
      </view>
      <view class="btn_fixed" @click="submit">
        <view class="btn_box">
          <view class="btn_bg">下一步</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { decryption } from '@/service/crypto'
import { identifyLicenseOcr, applyOne } from '@/interPost/company'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
const { changeIdent } = useChangeIdent()
const { getToken } = useUserInfo()
// 图片
const fromData = ref({
  companyName: '',
  creditCode: '',
  id: null,
  legalPerson: '',
})
// 图片id
const fileIdObj = ref({
  fileId: '',
})
const fileList = ref([])
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
// 图片上传
const baseUrl =
  import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThumPrivate'
// 下一步
const submit = async () => {
  if (!fileIdObj.value.fileId) {
    uni.showToast({
      title: '请上传营业执照',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.companyName) {
    uni.showToast({
      title: '请输入公司全称',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.legalPerson) {
    uni.showToast({
      title: '请输入公司法人',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.creditCode) {
    uni.showToast({
      title: '统一社会信用代码',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  const res: any = await applyOne({ ...fromData.value, ...fileIdObj.value })
  if (res.code === 0) {
    uni.reLaunch({
      url: `/loginSetting/companyJoin/recrIdent`,
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 切换身份
const changeIdentFun = async () => {
  changeIdent()
}
// 获取图片信息
const getimgInfo = async (id: any) => {
  const res: any = await identifyLicenseOcr({ id })
  console.log(res, '========')
  if (res.code === 0) {
    fromData.value = res.data
  }
}
// 图片上传成功
const successFun = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  // const res = JSON.parse(decryption(resJson.data))
  if (res.code === 0) {
    fileIdObj.value.fileId = res.data[0].fileId
    getimgInfo(res.data[0].fileId)
  }
  console.log(res, '成功的返回')
}
</script>

<style lang="scss" scoped>
::v-deep .custom-class {
  width: 460rpx !important;
  height: 320rpx !important;
}
::v-deep .wd-upload__preview {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
}

.containner-page {
  padding: 40rpx;
  .containner-title {
    margin-bottom: 70rpx;
    font-size: 60rpx;
    font-weight: 600;
  }
  .containner-group {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 60rpx;
    .containner-group-img {
      width: 460rpx;
      height: 320rpx;
    }
    .containner-group-text {
      position: absolute;
      bottom: 20rpx;
      left: 120rpx;
      padding: 0rpx 40rpx;
      background-color: rgba(35, 35, 35, 0.34);
      border-radius: 60rpx;
    }
  }
  .containner-card {
    padding: 20rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
    .sb-name {
      color: rgba(136, 136, 136, 1);
    }
    .sb-name-t {
      color: rgba(51, 51, 51, 1);
    }
  }
  .containner-subText {
    margin-top: 30rpx;
    .containner-subText-name {
      font-size: 24rpx;
      line-height: 1.5;
      color: rgba(136, 136, 136, 1);
    }
  }
  .btn_fixed {
    position: fixed;
    right: 60rpx;
    bottom: 100rpx;
    left: 60rpx;
    box-sizing: border-box;
    width: calc(100% - 120rpx);
    .btn_box {
      .btn_bg {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 30rpx 0rpx;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
        border-radius: 14px 14px 14px 14px;
      }
    }
  }
}
</style>
