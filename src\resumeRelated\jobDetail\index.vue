<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" layout-only>
    <template #top>
      <CustomNavBar>
        <template #right>
          <wd-img
            :width="22"
            :height="22"
            :src="collect"
            v-if="objJibDeatil.collectStatus === 0"
            @click="collectFun"
          />
          <wd-img
            :width="22"
            :height="22"
            :src="noCollect"
            v-if="objJibDeatil.collectStatus === 1"
            @click="nocollectFun"
          />
        </template>
      </CustomNavBar>
    </template>

    <view class="jobDetail-list">
      <view class="jobDetail-title flex-between">
        <view class="jobDetail-title-main">{{ objJibDeatil.positionName }}</view>
        <view class="jobDetail-title-salary">
          {{ objJibDeatil.workSalaryBegin }}-{{ objJibDeatil.workSalaryEnd }}
        </view>
      </view>
      <view class="jobDetail-subtitle">
        <view class="flex-c">
          <wd-icon name="location" size="16px" color="#888"></wd-icon>
          <view class="m-l-10rpx subText">{{ objJibDeatil.location }}</view>
        </view>
        <view class="subText jobDetail-subtitle-text" v-if="objJibDeatil.isToDayPublish">
          该职位于今日新发布
        </view>
      </view>
      <view class="jobDetail-card flex-c" @click="goRecruiter">
        <view class="jobDetail-card-avatar-container">
          <view class="jobDetail-card-bg-ellipse"></view>
          <image
            v-if="objJibDeatil.sex === 1"
            class="jobDetail-card-img"
            :src="objJibDeatil.hrUrl ? objJibDeatil.hrUrl : '/static/header/jobhunting1.png'"
            mode="aspectFill"
          ></image>
          <image
            v-else
            class="jobDetail-card-img"
            :src="objJibDeatil.hrUrl ? objJibDeatil.hrUrl : '/static/header/jobhunting2.png'"
            mode="aspectFill"
          ></image>
        </view>
        <view class="jobDetail-card-bg">
          <view class="jobDetail-card-info">
            <view class="jobDetail-card-content">
              <view class="jobDetail-card-name text-28rpx p-b-6rpx">
                {{ objJibDeatil.hrPositionName }}·
                <text class="c-#666 text-24rpx">{{ objJibDeatil.hrPosition }}</text>
              </view>
              <view class="subText">{{ objJibDeatil.company }}</view>
              <view class="subText">今日回复6次</view>
            </view>
            <view class="jobDetail-chat-icon">
              <image
                class="chat-icon"
                src="/static/images/home/<USER>"
                mode="aspectFill"
              ></image>
            </view>
          </view>
        </view>
      </view>

      <view class="jobDetail-content">
        <view class="jobDetail-content-title p-t-20rpx">岗位详情</view>
        <view class="jobDetail-tag-list flex-c m-b-20rpx" v-if="objJibDeatil.positionKey">
          <view
            class="jobDetail-tag"
            v-for="(item, index) in objJibDeatil.positionKey"
            :key="index"
          >
            {{ item }}
          </view>
        </view>
        <view class="jobDetail-content-name">岗位要求</view>
        <div class="jobDetail-content-name text-pre-wrap">{{ objJibDeatil.positionDesc }}</div>
      </view>
      <view class="jobDetail-content" v-if="objJibDeatil.positionBenefitVOList">
        <view class="jobDetail-content-title p-t-40rpx">福利待遇</view>
        <view class="jobDetail-tag-list flex-c">
          <view
            class="jobDetail-tag"
            v-for="(item, index) in objJibDeatil.positionBenefitVOList"
            :key="index"
          >
            {{ item }}
          </view>
        </view>
      </view>
      <view class="jobDetail-content-name m-t-20rpx">工作地址</view>
      <view class="jobDetail-image-list" @click="goMap">
        <!-- <image class="jobDetail-image" :src="imgMap"></image> -->

        <view class="flex-c">
          <wd-icon name="location" size="16px" color="#888"></wd-icon>
          <view class="subText">{{ objJibDeatil.regAddress }}</view>
        </view>
      </view>
      <view class="jobDetail-conpany flex-between" @click="goCompany">
        <view class="flex-c">
          <!-- <image class="jobDetail-conpany-img" src="/static/img/1.jpg"></image> -->
          <image
            class="jobDetail-conpany-img"
            :src="
              objJibDeatil.companyLogoUrl ? objJibDeatil.companyLogoUrl : '/static/header/logo.png'
            "
            mode="aspectFill"
          ></image>
          <view class="jobDetail-conpany-text">
            <view class="jobDetail-conpany-title">{{ objJibDeatil.company }}</view>
            <view class="flex-c">
              <view class="jobDetail-conpany-subtitle p-r-40rpx">{{ objJibDeatil.sizeName }}</view>
              <view class="jobDetail-conpany-subtitle">{{ objJibDeatil.industryName }}</view>
            </view>
          </view>
        </view>
        <view class="jobDetail-card-icon">
          <wd-icon color="#317CFF" name="chevron-right" size="15px" class="arrow-right-1"></wd-icon>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn_fixed" @click="goChat">
        <view class="btn_box">
          <view class="btn_bg">立即沟通</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { positionInfoDetil, queryKey, cancelPosition, collectPosition } from '@/interPost/home'
import { numberTokw } from '@/utils/common'

import collect from '@/static/img/positionSc.png'
import noCollect from '@/static/img/positionQxsc.png'

const message = useMessage()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const { userRoleIsRealName } = useUserInfo()
const { sendGreetingMessage } = useIMConversation()
// id
const id = ref(null)
// companyId
const companyId = ref(null)
const objJibDeatil = ref<AnyObject>({})
const imgMap = ref('')
// 详情
const getDetail = async () => {
  const res: any = await positionInfoDetil({ id: id.value })
  if (res.code === 0) {
    res.data.positionKey = res.data.positionKey ? res.data.positionKey.split(',') : ''
    res.data.workSalaryBegin =
      res.data.workSalaryBegin === 0 ? '面议' : numberTokw(res.data.workSalaryBegin + '')
    res.data.workSalaryEnd =
      res.data.workSalaryEnd === 0 ? '面议' : numberTokw(res.data.workSalaryEnd + '')
    objJibDeatil.value = res.data
  }
}
// 收藏
const collectFun = async () => {
  const res: any = await collectPosition({ id: objJibDeatil.value.id })
  if (res.code === 0) {
    objJibDeatil.value.collectStatus = 1
  }
}
// 取消收藏
const nocollectFun = async () => {
  const res: any = await cancelPosition({ id: objJibDeatil.value.id })
  if (res.code === 0) {
    objJibDeatil.value.collectStatus = 0
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  // 主健id
  id.value = options.id
  // 公司id
  companyId.value = options.companyId
  await getDetail()
  await getMapKet()
})
const goMap = () => {
  uni.openLocation({
    latitude: Number(objJibDeatil.value.lat), // 目标纬度
    longitude: Number(objJibDeatil.value.lon), // 目标经度
    name: objJibDeatil.value.company, // 显示在地图上的标记名称
    address: objJibDeatil.value.regAddress, // 辅助信息
    success: () => console.log('跳转成功'),
    fail: (err) => console.error('跳转失败', err),
  })
}
// 获取ket
const getMapKet = async () => {
  const res = await queryKey()
  // 纬度
  const lat = objJibDeatil.value.lat
  // 精度
  const lon = objJibDeatil.value.lon
  const staticKey = res.data.staticKey
  imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${lon},${lat}&width=300&height=200&zoom=12&tk=${staticKey}&markers=${lon},${lat}`
}
const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}
// 去沟通
const goChat = async () => {
  try {
    await userToRealName()
    const hxUserInfoVO = objJibDeatil.value?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      sendGreetingMessage(hxUserInfoVO.username, objJibDeatil.value)
    }
  } catch (error) {}
}
const goRecruiter = () => {
  const hrId = objJibDeatil.value.hrId
  uni.navigateTo({
    url: `/resumeRelated/recruiter/index?companyId=${companyId.value}&hrId=${hrId}`,
  })
}

const goCompany = () => {
  uni.navigateTo({
    url: `/resumeRelated/company/index?companyId=${companyId.value}&id=${objJibDeatil.value.id}`,
  })
}
</script>

<style lang="scss" scoped>
.btn_fixed {
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    padding: 0rpx 40rpx 20rpx;
    margin-top: 30rpx;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 30rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.jobDetail-list {
  padding: 40rpx;

  .jobDetail-title {
    .jobDetail-title-main {
      font-size: 36rpx;
      font-weight: bold;
      line-height: 44rpx;
      color: #000;
    }

    .jobDetail-title-salary {
      font-size: 28rpx;
      color: #ff5050;
    }
  }

  .jobDetail-subtitle {
    padding-top: 20rpx;

    .jobDetail-subtitle-text {
      line-height: 44rpx;
    }
  }

  .jobDetail-card {
    padding: 40rpx 0rpx 20rpx;

    .jobDetail-card-img {
      width: 120rpx;
      height: 120rpx;
      border-radius: 20rpx;
    }

    .jobDetail-card-bg {
      display: flex;
      flex-direction: column;
      align-items: left;
      justify-content: center;
      width: calc(100% - 90rpx);
      padding: 20rpx 20rpx 20rpx 70rpx;
      margin-left: -50rpx;
      background-color: #ffffff;
      border-radius: 36rpx;
    }

    .jobDetail-card-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .jobDetail-card-content {
        flex: 1;
      }

      .jobDetail-chat-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80rpx;
        height: 80rpx;
        flex-shrink: 0;

        .chat-icon {
          width: 48rpx;
          height: 48rpx;
          opacity: 0.7;
          &_icon {
            width: 40rpx;
            height: 40rpx;
          }
          &_icon-1 {
            width: 40rpx;
            height: 40rpx;
          }
        }
      }
    }

    .jobDetail-card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      margin-left: -40rpx;
      background-color: #aeffb1;
      border-radius: 20rpx;

      .arrow-right-1 {
        line-height: 60rpx;
      }
    }
  }

  .jobDetail-tag-list {
    flex-wrap: wrap;
    padding: 30rpx 0rpx 0rpx;

    .jobDetail-tag {
      // display: flex;
      // flex-wrap: wrap;
      // align-items: center;
      padding: 5rpx 30rpx;
      margin-right: 20rpx;
      margin-bottom: 20rpx;
      font-size: 26rpx;
      color: #555555;
      text-align: center;
      background-color: #ebebeb;
      border-radius: 16rpx;
    }
  }

  .jobDetail-content {
    .jobDetail-content-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #000000;
    }

    .jobDetail-content-name {
      font-size: 28rpx;
      line-height: 50rpx;
      color: #555555;
    }
  }

  .jobDetail-conpany {
    padding: 20rpx;
    margin: 40rpx 0rpx;
    background: #ffffff;

    .jobDetail-conpany-img {
      width: 120rpx;
      height: 120rpx;
      border-radius: 34rpx;
    }

    .jobDetail-conpany-text {
      margin-left: 20rpx;

      .jobDetail-conpany-title {
        padding-bottom: 10rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #000000;
      }

      .jobDetail-conpany-subtitle {
        font-size: 24rpx;
        color: #000000;
      }
    }

    .jobDetail-card-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60rpx;
      height: 60rpx;
      background-color: #bce9ff;
      border-radius: 20rpx;

      .arrow-right-1 {
        line-height: 60rpx;
      }
    }
  }

  .jobDetail-image-list {
    margin: 40rpx 0rpx 60rpx;

    .jobDetail-image {
      width: 100%;
      height: 320rpx;
    }
  }
}
</style>
