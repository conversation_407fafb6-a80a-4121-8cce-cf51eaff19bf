<template>
  <!-- 新增 fixed 定位容器 -->
  <view>
    <!-- 固定定位的导航栏 -->
    <view
      ref="customNavbar"
      class="customNavbar customNavbar-bg"
      :style="
        fixed
          ? {
              background: bgColor,
              position: 'fixed',
              top: '0',
              left: '0',
              'z-index': 99999,
              backgroundImage: bgImg,
            }
          : {
              backgroundImage: bgImg,
              background: bgColor,
            }
      "
    >
      <!-- 系统状态栏占位 -->
      <view class="system" :style="{ height: `${statusBar * 2}rpx` }"></view>
      <!-- 导航栏主体 -->
      <view class="navbar" :style="{ height: `${(customBar - statusBar) * 2}rpx` }">
        <view class="left">
          <slot name="left">
            <wd-icon @click="back" name="arrow-left" class="back-button" :color="color" size="20" />
          </slot>
        </view>
        <view class="content">
          <slot name="content">{{ title }}</slot>
        </view>
        <view class="right">
          <slot name="right"></slot>
        </view>
      </view>
      <view class="navbar-list" :style="{ height: `${tabHeight * 2}rpx` }">
        <slot name="contentText"></slot>
      </view>
    </view>
    <!-- 占位视图防止内容被遮挡 -->
    <view :style="{ height: `${(customBar + tabHeight) * 2}rpx` }"></view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { setCustomBar, setStatusBar } from '@/utils/storege'
const statusBar = ref(0)
const customBar = ref(0)
const platform = ref('')
const isPageUp = ref(true)
defineProps({
  tabHeight: {
    type: Number,
    default: 0,
  },
  color: {
    type: String,
    default: '#333333',
  },
  leftText: {
    type: String,
    default: '',
  },
  title: {
    type: String,
    default: '',
  },
  rightText: {
    type: String,
    default: '',
  },
  bgColor: {
    type: String,
    default: 'transparent',
  },
  bgImg: {
    type: String,
    default: '',
  },
  fixed: {
    type: Boolean,
    default: true,
  },
})
onLoad(() => {
  uni.getSystemInfo({
    success: (res) => {
      platform.value = res.platform
      statusBar.value = res.statusBarHeight
      customBar.value = res.statusBarHeight + 45
      if (res.platform === 'android') {
        statusBar.value += 3
        customBar.value += 3
      }

      setCustomBar(customBar.value)
      setStatusBar(statusBar.value)
    },
  })
  const pages = getCurrentPages()
  isPageUp.value = !!pages[pages.length - 2]
})
const back = () => {
  uni.navigateBack()
}

const homePage = () => {
  uni.switchTab({
    url: '/pages/home/<USER>',
  })
}
</script>

<style lang="scss" scoped>
.customNavbar {
  width: 100%;

  .system {
    width: 100%;
    // background: transparent;
  }

  .navbar {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 30rpx;
  }

  .left,
  .right {
    display: flex;
    align-items: center;
    width: 120rpx;
  }

  .left {
    justify-content: flex-start;
  }

  .content {
    flex: 1;
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }

  .right {
    justify-content: flex-end;
  }
}
</style>
