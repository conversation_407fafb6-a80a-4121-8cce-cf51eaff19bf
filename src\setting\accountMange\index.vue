<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="账号管理"></CustomNavBar>
    <view class="setting">
      <view class="setting-list border-b">
        <view class="flex-between">
          <view class="text-32rpx font-w-500">修改手机号</view>
          <view class="subText flex-c">
            <view class="p-r-10rpxrpx">177 **** **77</view>
            <wd-icon
              name="chevron-right"
              size="20px"
              color="#888888"
              class="arrow-right-icon"
            ></wd-icon>
          </view>
        </view>

        <view class="subText">修改成功后，可通过新手机号登陆易直聘</view>
      </view>

      <view class="setting-list flex-between border-b">
        <view class="text-32rpx font-w-500">修改密码</view>

        <wd-icon
          name="chevron-right"
          size="20px"
          color="#888888"
          class="arrow-right-icon"
        ></wd-icon>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const goAccountNumber = () => {
  uni.navigateTo({
    url: '/setting/accountNumber/index',
  })
}
</script>
<style scoped lang="scss">
// 样式
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}

.setting-text {
  padding-bottom: 40rpx;
  font-size: 22rpx;
  color: #888888;
  border-bottom: 2rpx solid #d7d6d6 !important;
}
</style>
