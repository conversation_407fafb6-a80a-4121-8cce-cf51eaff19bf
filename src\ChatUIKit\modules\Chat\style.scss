.chat-wrap {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  background: linear-gradient( 135deg, #FFC8C8 0%, #EDF1FF 37%, #FFFFFF 100%);
}

.chat-wrap-keyboard-close {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.msgs-wrap {
  flex: 1;
  position: relative;
  overflow: hidden;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
}

.chat-input-wrap {
  padding: 32rpx 50rpx 60rpx;
  box-sizing: border-box;
  width: 100%;
  background-color: #FFFFFF;
  flex-shrink: 0;
}

.mask {
  position: absolute;
  height: 100%;
  width: 100vw;
  z-index: 999;
}