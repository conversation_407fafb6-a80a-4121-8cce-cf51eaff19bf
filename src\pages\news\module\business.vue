<template>
  <z-paging ref="pagingRef" auto v-model="pageData" :paging-style="pageStyle" @query="queryList">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          safe-area-inset-top
          placeholder
          custom-class="!bg-transparent px-30rpx"
        >
          <template #left>
            <wd-img :src="thunderPromptImg" width="54rpx" height="54rpx" />
          </template>
          <template #right>
            <view class="flex items-center gap-30rpx">
              <wd-img :src="messagePromptImg" width="54rpx" height="54rpx" />
              <wd-img :src="setupImg" width="54rpx" height="54rpx" />
            </view>
          </template>
        </wd-navbar>
        <view class="px-40rpx">
          <wd-img :src="newsDefaultBanner" width="100%" height="156rpx" custom-class="mt-20rpx" />
          <wd-search
            placeholder-left
            hide-cancel
            custom-class="!p-0 !bg-transparent mt-28rpx mb-60rpx"
            placeholder="搜索联系人、聊天记录、备注"
          />
          <view class="flex items-center flex items-stretch gap-56rpx">
            <common-card class="flex-1">
              <view class="h-156rpx flex flex-col justify-center gap-6rpx px-26rpx">
                <wd-img :src="newsGreetings" width="36rpx" height="36rpx" />
                <text class="text-24rpx c-#4075FF">新招呼</text>
                <text class="text-24rpx c-#4075FF font-500">New greetings</text>
              </view>
              <wd-img
                :src="newsPeople"
                width="168rpx"
                height="168rpx"
                custom-class="!absolute right-0 top--52rpx"
              />
            </common-card>
            <view class="flex flex-col gap-16rpx flex-1">
              <common-card class="flex-1">
                <view class="center h-full box-border px-32rpx gap-4rpx">
                  <text class="line-clamp-1 flex-1 c-#000000 text-24rpx">直播运营</text>
                  <view class="flex items-center gap-2rpx">
                    <text class="c-#000000 text-24rpx">筛选</text>
                    <text class="i-carbon-caret-down" />
                  </view>
                </view>
              </common-card>
              <view class="flex items-center gap-24rpx flex-1">
                <common-card class="flex-1 h-full">
                  <view class="center h-full">
                    <text class="c-#000000 text-24rpx">未读</text>
                  </view>
                </common-card>
                <common-card class="flex-1 h-full">
                  <view class="center h-full">
                    <text class="c-#000000 text-24rpx">已约面</text>
                  </view>
                </common-card>
              </view>
            </view>
          </view>
        </view>
        <wd-gap bg-color="#DEDDDD" height="1px" custom-class="mt-38rpx" />
      </wd-config-provider>
    </template>
    <view class="px-40rpx py-38rpx">
      <newsBusinessList :news-list="pageData" />
    </view>
    <template #bottom>
      <customTabbar name="news" />
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import customTabbar from '@/components/common/custom-tabbar.vue'
import commonCard from '@/components/common/common-card.vue'
import newsBusinessList from '@/components/news/news-business-list.vue'
import setupImg from '@/static/common/setup.png'
import messagePromptImg from '@/static/common/message-prompt.png'
import thunderPromptImg from '@/static/common/thunder-prompt.png'
import newsDefaultBanner from '@/static/news/business/news-default-banner.png'
import newsGreetings from '@/static/news/business/news-greetings.png'
import newsPeople from '@/static/news/business/news-people.png'

defineOptions({
  name: 'NewsBusiness',
})
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const themeVars: ConfigProviderThemeVars = {
  searchInputBg: 'rgba(61,61,61,0.34)',
  searchInputHeight: '82rpx',
  searchInputRadius: '80rpx',
  searchPlaceholderColor: '#FFFFFF',
  searchIconSize: '36rpx',
  searchIconColor: '#FFFFFF',
  searchInputFs: '24rpx',
  searchInputColor: ' #FFFFFF',
  searchCancelColor: '#FFFFFF',
}

const queryList = async () => {
  const data = Array.from({ length: 10 }, (_, i) => ({
    id: `${i}`,
    name: `面试人员${i}`,
    date: new Date().toLocaleDateString(),
  }))
  pagingRef.value.completeByTotal(data, 10)
}
</script>

<style lang="scss" scoped></style>
