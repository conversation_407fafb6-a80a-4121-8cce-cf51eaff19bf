<template>
  <common-card>
    <view class="p-[34rpx_30rpx_24rpx]">
      <view class="flex items-start gap-20rpx">
        <view class="flex items-center gap-14rpx">
          <wd-img :src="avatarOne" width="72rpx" height="72rpx" round />
          <view class="flex flex-col gap-4rpx">
            <text class="c-#555555 text-28rpx font-500">张先生</text>
            <text class="c-#888888 text-22rpx">期望薪资 · 8k-12k</text>
          </view>
        </view>
        <view class="flex flex-col items-end flex-1 gap-24rpx">
          <view class="flex items-center flex-wrap gap-22rpx">
            <view
              class="bg-#F3F3F3 border-rd-6rpx center h-36rpx min-w-124rpx px-10rpx"
              v-for="(item, key) in ['本科', '1-3年']"
              :key="key"
            >
              <text class="c-#888888 text-22rpx">{{ item }}</text>
            </view>
          </view>
          <view class="center bg-#D7DFFF h-36rpx border-rd-6rpx px-14rpx gap-6rpx">
            <wd-img :src="certificateMark" width="20rpx" height="20rpx" />
            <text class="c-#4075FF text-22rpx">机床上岗资格证书</text>
          </view>
        </view>
      </view>
      <view class="flex items-center">
        <wd-img :src="postMark" width="26rpx" height="26rpx" />
        <text class="c-#888888 text-22rpx">&nbsp;·&nbsp;电商运营直播运营</text>
      </view>
      <view class="flex flex-col gap-14rpx mt-18rpx">
        <text class="c-#888888 text-22rpx line-clamp-2">
          我的优势：文本内容文本内容文本内容文本内容文本内容文本内容文本内容文本内容文本内容文本内容
        </text>
        <view class="flex items-center flex-wrap gap-20rpx">
          <view
            class="bg-#F3F3F3 border-rd-6rpx h-36rpx min-w-150rpx px-20rpx center"
            v-for="(item, key) in ['技能标签1', '技能标签2', '技能标签3']"
            :key="key"
          >
            <text class="c-#888888 text-22rpx">{{ item }}</text>
          </view>
        </view>
      </view>
    </view>
  </common-card>
</template>

<script lang="ts" setup>
import commonCard from './common-card.vue'
import avatarOne from '@/static/common/avatar/1.png'
import certificateMark from '@/static/common/certificate-mark.png'
import postMark from '@/static/common/post-mark.png'
</script>

<style lang="scss" scoped>
//
</style>
